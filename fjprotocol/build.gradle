plugins {
	alias(libs.plugins.android.library)
}

android {
	namespace 'com.fj.fjprotocol'
	compileSdk versions.compileSdk

	defaultConfig {
		minSdk versions.minSdk
		targetSdk versions.targetSdk

		testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
		consumerProguardFiles "consumer-rules.pro"
	}

	buildTypes {
		release {
			minifyEnabled false
			proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
		}
	}

	buildFeatures {
		buildConfig true
	}

	compileOptions {
		sourceCompatibility JavaVersion.VERSION_21
		targetCompatibility JavaVersion.VERSION_21
	}

	packagingOptions {
		jniLibs {
			pickFirsts += ['**/libc++_shared.so']
		}
	}
}

dependencies {
	implementation project(path: ':logger')
	implementation project(path: ':common')
	implementation libs.fjdynamics.protocollibrary
	api(libs.fjdynamics.tractorprotocol) {
		exclude group: 'org.slf4j'
		exclude group: 'org.apache.mina'
		exclude group: 'com.tencent.mars'
	}
	implementation(libs.okio)

	testImplementation libs.junit
	androidTestImplementation libs.androidx.junit
	androidTestImplementation libs.androidx.test.espresso
}
