package com.fj.fjprotocol;

import static com.fj.fjprotocol.ProtocolConstants.CONFIG_SLOWDOWN_DELAY;
import static com.fj.fjprotocol.ProtocolConstants.DISCRETE_COMMAND;
import static com.fj.fjprotocol.ProtocolConstants.ECU_VERSION;
import static com.fj.fjprotocol.ProtocolConstants.HEARTBEAT;
import static com.fj.fjprotocol.ProtocolConstants.MOCK_ECU_RESTART;
import static com.fj.fjprotocol.ProtocolConstants.OTA_CMD;
import static com.fj.fjprotocol.ProtocolConstants.OTA_FILE_INFO;
import static com.fj.fjprotocol.ProtocolConstants.OTA_SEND_FILE;
import static com.fj.fjprotocol.ProtocolConstants.OTA_START_UPGRADE;
import static com.fj.fjprotocol.ProtocolConstants.SANY_CONTROL_COMMAND;
import static com.fj.fjprotocol.ProtocolConstants.SANY_LIFTING;
import static com.fj.fjprotocol.ProtocolConstants.SANY_POWER;
import static com.fj.fjprotocol.ProtocolConstants.SANY_RESET;
import static com.fj.fjprotocol.ProtocolConstants.SANY_SPIN;
import static com.fj.fjprotocol.ProtocolConstants.SANY_SPIN_BREAK;
import static com.fj.fjprotocol.ProtocolConstants.SANY_STOP;
import static com.fj.fjprotocol.ProtocolConstants.SANY_VARIATION;
import static com.fj.fjprotocol.ProtocolConstants.SEND_YONGMAO_DATA;
import static com.fj.fjprotocol.ProtocolConstants.SET_ALARM_CONFIG;
import static com.fj.fjprotocol.ProtocolConstants.SET_ARM;
import static com.fj.fjprotocol.ProtocolConstants.SET_CARGO_SIZE;
import static com.fj.fjprotocol.ProtocolConstants.SET_CURRENT_LOAD;
import static com.fj.fjprotocol.ProtocolConstants.SET_INCLINATION_OFFSET;
import static com.fj.fjprotocol.ProtocolConstants.SET_LOAD_WARN_TABLE;
import static com.fj.fjprotocol.ProtocolConstants.SET_LOCATION_A;
import static com.fj.fjprotocol.ProtocolConstants.SET_LOCATION_B;
import static com.fj.fjprotocol.ProtocolConstants.SET_LOCATION_C;
import static com.fj.fjprotocol.ProtocolConstants.SET_OBSTACLE_POINTS;
import static com.fj.fjprotocol.ProtocolConstants.SET_OBSTACLE_WARN;
import static com.fj.fjprotocol.ProtocolConstants.SET_RADAR_DISTANCE;
import static com.fj.fjprotocol.ProtocolConstants.SET_SEMIAUTOMATIC_HEIGHT;
import static com.fj.fjprotocol.ProtocolConstants.SET_TEST_MODE;
import static com.fj.fjprotocol.ProtocolConstants.SET_TOWER;
import static com.fj.fjprotocol.ProtocolConstants.SET_TROLLEY_WIDTH;
import static com.fj.fjprotocol.ProtocolConstants.SET_WIND_SPEED_WARN;
import static com.fj.fjprotocol.ProtocolConstants.SET_WORKING_MODE;
import static com.fj.fjprotocol.ProtocolConstants.START_SEMIAUTOMATIC_TASK_REQ;
import static com.fj.fjprotocol.ProtocolConstants.UPDATE_333_ALARM;
import static com.fj.fjprotocol.ProtocolConstants.UPDATE_CUTOFF_AND_LIGHT_SIGNAL;
import static com.fj.fjprotocol.ProtocolConstants.UPDATE_DRIVER_LOGIN_STATUS;
import static com.fj.fjprotocol.ProtocolConstants.UPDATE_SEMIAUTOMATIC_TASK;
import static com.fj.fjprotocol.ProtocolConstants.UPDATE_TOWER_TYPE;
import static com.fj.fjprotocol.ProtocolConstants.UPDATE_WEATHER_STATION_DATA;

import android.util.Log;
import android.util.Pair;

import com.fj.fjprotocol.data.GeoData;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.fjdynamics.tractorprotocol.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 塔吊ecu协议封住
 *
 * <AUTHOR>
 */
public class ProtocolHelper {

	private static final String TAG = "ProtocolHelper";

	/**
	 * 手动模式
	 */
	public static final int WORKING_MODE_MANUAL = 0;
	/**
	 * 遥控模式
	 */
	public static final int WORKING_MODE_REMOTE = 1;
	/**
	 * 轨迹复现/智能规划模式：移动至A点
	 */
	public static final int WORKING_MODE_A = 2;
	/**
	 * 智能规划模式：移动至B点
	 */
	public static final int WORKING_MODE_SMART_PLANNING_B = 3;
	/**
	 * 智能规划模式：吊钩上升到最高点
	 */
	public static final int WORKING_MODE_UP = 4;
	/**
	 * 空闲模式：退出半自动模式
	 */
	public static final int WORKING_MODE_FREE = 5;
	/**
	 * 轨迹复现模式：移动到B点
	 */
	public static final int WORKING_MODE_B = 6;
	/**
	 * A点
	 */
	public static final int TYPE_A = 1;
	/**
	 * B点
	 */
	public static final int TYPE_B = 2;
	/**
	 * 旋转中心点
	 */
	public static final int TYPE_C = 3;

	/**
	 * 发送心跳包
	 *
	 * @return data
	 */
	public static byte[] heartbeat() {
		byte[] reqDataDM = new byte[]{HEARTBEAT};
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(0));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 查询ecu版本号
	 *
	 * @return data
	 */
	public static byte[] queryEcuVersion() {
		Logger.d(TAG, "queryEcuVersion");
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = DataUtil.int2byte1(ECU_VERSION);
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, cmdIdBytes);
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置工作模式
	 *
	 * @param mode 工作模式,例如{@link #WORKING_MODE_MANUAL}
	 * @return data
	 */
	public static byte[] setWorkingMode(int mode) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = DataUtil.int2byte1(SET_WORKING_MODE);
		byte[] cmdDataBytes = DataUtil.int2byte1(mode);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置经纬高
	 *
	 * @param type      1.A点 2.B点 3.旋转中心
	 * @param latitude  纬度
	 * @param longitude 经度
	 * @param height    高度
	 * @return data
	 */
	public static byte[] setLocation(int type, double latitude, double longitude, double height) {
		Logger.d(TAG, "setLocation: type -> " + type + ", lat -> " + latitude + ", lon -> " + longitude + ", alt -> " + height);
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = new byte[1];
		if (type == TYPE_A) {
			cmdIdBytes[0] = SET_LOCATION_A;
		} else if (type == TYPE_B) {
			cmdIdBytes[0] = SET_LOCATION_B;
		} else if (type == TYPE_C) {
			cmdIdBytes[0] = SET_LOCATION_C;
		} else {
			Log.e(TAG, "setLocation: type not match -> " + type);
			return null;
		}

		byte[] cmdDataBytes = new byte[8 + 8 + 4];
		byte[] latBytes = DataUtil.longToByteArray((long) (latitude * Math.pow(10, 10)));
		byte[] lngBytes = DataUtil.longToByteArray((long) (longitude * Math.pow(10, 10)));
		byte[] heightBytes = DataUtil.int2byte((int) (height * 10_000));
		System.arraycopy(latBytes, 0, cmdDataBytes, 0, latBytes.length);
		System.arraycopy(lngBytes, 0, cmdDataBytes, 8, lngBytes.length);
		System.arraycopy(heightBytes, 0, cmdDataBytes, 16, heightBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置吊臂长度及远近端安全距离
	 *
	 * @param armLength    吊臂长度
	 * @param nearDistance 近端安全距离
	 * @param farDistance  远端安全距离
	 * @return data
	 */
	public static byte[] setArm(double armLength, double nearDistance, double farDistance) {
		Logger.d(TAG, "setArm: armLength -> " + armLength + ", nearDistance -> " + nearDistance + ", farDistance -> " + farDistance);
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_ARM};

		byte[] cmdDataBytes = new byte[4 + 4 + 4];
		byte[] armLenBytes = DataUtil.intToByte4((int) (armLength * 10_000));
		byte[] nearDistanceBytes = DataUtil.intToByte4((int) (nearDistance * 10_000));
		byte[] farDistanceBytes = DataUtil.intToByte4((int) (farDistance * 10_000));
		System.arraycopy(armLenBytes, 0, cmdDataBytes, 0, armLenBytes.length);
		System.arraycopy(nearDistanceBytes, 0, cmdDataBytes, 4, nearDistanceBytes.length);
		System.arraycopy(farDistanceBytes, 0, cmdDataBytes, 8, farDistanceBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置塔吊高度及安全距离
	 *
	 * @param towerHeight    塔吊高度
	 * @param topDistance    上方安全距离
	 * @param bottomDistance 下方安全距离
	 * @return data
	 */
	public static byte[] setTower(double towerHeight, double topDistance, double bottomDistance) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_TOWER};

		byte[] cmdDataBytes = new byte[4 + 4 + 4];
		byte[] towerHeightBytes = DataUtil.intToByte4((int) (towerHeight * 10_000));
		byte[] topDistanceBytes = DataUtil.intToByte4((int) (topDistance * 10_000));
		byte[] bottomDistanceBytes = DataUtil.intToByte4((int) (bottomDistance * 10_000));
		System.arraycopy(towerHeightBytes, 0, cmdDataBytes, 0, towerHeightBytes.length);
		System.arraycopy(topDistanceBytes, 0, cmdDataBytes, 4, topDistanceBytes.length);
		System.arraycopy(bottomDistanceBytes, 0, cmdDataBytes, 8, bottomDistanceBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置台架模式
	 *
	 * @param enabled 是否打开台架模式
	 * @return data
	 */
	public static byte[] setTestMode(boolean enabled) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_TEST_MODE};

		byte[] cmdDataBytes = DataUtil.int2byte1(enabled ? 1 : 0);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * app透传吊钩gga数据
	 *
	 * @param nmeaData 吊钩nmea数据
	 * @return data
	 */
	public static byte[] sendHookGgaData(byte[] nmeaData) {
		byte[] cmdSet = {(byte) 0xd1};
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, nmeaData);
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * app透传小车gga
	 *
	 * @param ggaStr gga字符串
	 * @return data
	 */
	public static byte[] sendGearGgaData(String ggaStr) {
		byte[] cmdSet = {(byte) 0xd2};
		byte[] cmdDataBytes = ggaStr.getBytes(StandardCharsets.UTF_8);
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, cmdDataBytes);
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	// 生成rtcm流报文
	public static byte[] reqQXData(byte[] data) {
		//        if (RtkManager.getInstance().isNewRtk()) {
		return data;
		//        } else {
		//            byte[] cmdSet = {(byte) 0xba};
		//            byte[] reqDataDM = DataUtil.byteMerger(cmdSet, data);
		//            DataRcToAPFormatter dataRcToAPFormatter = new DataRcToAPFormatter();
		//            dataRcToAPFormatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		//            dataRcToAPFormatter.setData(reqDataDM);
		//            return dataRcToAPFormatter.getFormattedData();
		//        }
	}

	/**
	 * 设置障碍物经纬高
	 *
	 * @param totalSize         障碍物总个数
	 * @param currentIndex      当前障碍物index
	 * @param obstaclePointList 障碍物列表
	 * @return data
	 */
	public static byte[] setObstaclePoints(
		int totalSize, int currentIndex, List<GeoData> obstaclePointList) {
		if (obstaclePointList == null || obstaclePointList.isEmpty()) {
			return null;
		}
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_OBSTACLE_POINTS};
		int latLen = 8;
		int lngLen = 8;
		int heightLen = 4;
		byte[] cmdDataBytes =
			new byte[1 + 1 + (latLen + lngLen + heightLen) * obstaclePointList.size()];

		int index = 0;
		// 障碍物总个数
		byte[] sizeBytes = DataUtil.int2byte1(totalSize);
		System.arraycopy(sizeBytes, 0, cmdDataBytes, index, sizeBytes.length);
		index += 1;
		// 当前障碍物index
		byte[] indexBytes = DataUtil.int2byte1(currentIndex);
		System.arraycopy(indexBytes, 0, cmdDataBytes, index, indexBytes.length);
		index += 1;

		for (int i = 0; i < obstaclePointList.size(); i++) {
			GeoData geoData = obstaclePointList.get(i);
			byte[] latBytes =
				DataUtil.longToByteArray((long) (geoData.getLat() * Math.pow(10, 10)));
			System.arraycopy(latBytes, 0, cmdDataBytes, index, latLen);
			index += latLen;
			byte[] lngBytes =
				DataUtil.longToByteArray((long) (geoData.getLng() * Math.pow(10, 10)));
			System.arraycopy(lngBytes, 0, cmdDataBytes, index, lngLen);
			index += lngLen;
			byte[] heightBytes = DataUtil.int2byte((int) (geoData.getAlt() * 10_000));
			System.arraycopy(heightBytes, 0, cmdDataBytes, index, heightLen);
			index += heightLen;
		}

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置吊物尺寸
	 *
	 * @param cargoLength 吊物长度
	 * @param cargoWidth  吊物宽度
	 * @param cargoHeight 吊物高度
	 * @param ropeHeight  吊绳高度
	 * @return data
	 */
	public static byte[] setCargoSize(
		double cargoLength, double cargoWidth, double cargoHeight, double ropeHeight) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_CARGO_SIZE};
		byte[] cmdDataBytes = new byte[4 + 4 + 4 + 4];

		byte[] cargoLengthBytes = DataUtil.intToByte4((int) (cargoLength * 10_000));
		byte[] cargoWidthBytes = DataUtil.intToByte4((int) (cargoWidth * 10_000));
		byte[] cargoHeightBytes = DataUtil.intToByte4((int) (cargoHeight * 10_000));
		byte[] ropeHeightBytes = DataUtil.intToByte4((int) (ropeHeight * 10_000));
		System.arraycopy(cargoLengthBytes, 0, cmdDataBytes, 0, cargoLengthBytes.length);
		System.arraycopy(cargoWidthBytes, 0, cmdDataBytes, 4, cargoWidthBytes.length);
		System.arraycopy(cargoHeightBytes, 0, cmdDataBytes, 8, cargoHeightBytes.length);
		System.arraycopy(ropeHeightBytes, 0, cmdDataBytes, 12, ropeHeightBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置避障预警
	 *
	 * @param enabled 避障预警开关——0：关,1：开
	 * @param warn    橙色避障阈值
	 * @param error   红色避障阈值
	 * @return data
	 */
	public static byte[] setObstacleWarn(boolean enabled, double warn, double error) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_OBSTACLE_WARN};
		byte[] cmdDataBytes = new byte[1 + 4 + 4];

		byte[] enabledBytes = DataUtil.int2byte1(enabled ? 1 : 0);
		byte[] warnBytes = DataUtil.intToByte4((int) (warn * 10_000));
		byte[] errorBytes = DataUtil.intToByte4((int) (error * 10_000));
		System.arraycopy(enabledBytes, 0, cmdDataBytes, 0, enabledBytes.length);
		System.arraycopy(warnBytes, 0, cmdDataBytes, 1, warnBytes.length);
		System.arraycopy(errorBytes, 0, cmdDataBytes, 5, errorBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置风速预警
	 *
	 * @param enabled 风速预警开关
	 * @param warn    风速预警阈值
	 * @return data
	 */
	public static byte[] setWindSpeedWarn(boolean enabled, double warn) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_WIND_SPEED_WARN};
		byte[] cmdDataBytes = new byte[1 + 2];

		byte[] enabledBytes = DataUtil.int2byte1(enabled ? 1 : 0);
		byte[] warnBytes = DataUtil.int2byte2((int) (warn * 100));
		System.arraycopy(enabledBytes, 0, cmdDataBytes, 0, enabledBytes.length);
		System.arraycopy(warnBytes, 0, cmdDataBytes, 1, warnBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置动态负载预警
	 *
	 * @param table 预警表
	 * @return data
	 */
	public static byte[] setLoadTable(List<Pair<Double, Double>> table) {
		Logger.d(TAG, "setLoadTable: " + CollectionUtils.getSize(table));
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_LOAD_WARN_TABLE};
		int size = table.size();
		int radiusLen = 2;
		int loadLen = 2;
		byte[] cmdDataBytes = new byte[1 + (radiusLen + loadLen) * size];

		int index = 0;
		// 表格长度
		byte[] sizeBytes = DataUtil.int2byte1(size);
		System.arraycopy(sizeBytes, 0, cmdDataBytes, index, sizeBytes.length);
		index += sizeBytes.length;

		for (int i = 0; i < table.size(); i++) {
			Pair<Double, Double> data = table.get(i);
			// 半径
			byte[] radiusBytes = DataUtil.int2byte2((int) (data.first * 100));
			System.arraycopy(radiusBytes, 0, cmdDataBytes, index, radiusLen);
			index += radiusLen;
			// 负载
			byte[] loadBytes = DataUtil.int2byte2((int) (data.second * 1.0));
			System.arraycopy(loadBytes, 0, cmdDataBytes, index, loadLen);
			index += loadLen;
		}

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置预警配置表
	 *
	 * @param alarmConfigList 预警配置表
	 * @return bytes
	 */
	public static byte[] setAlarmConfigList(List<Object[]> alarmConfigList) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_ALARM_CONFIG};
		int size = alarmConfigList.size();
		int typeLen = 1;
		int levelLen = 1;
		int comparatorLen = 1;
		int valueLen = 2;
		byte[] cmdDataBytes = new byte[1 + (typeLen + levelLen + comparatorLen + valueLen) * size];

		int index = 0;
		// 表格长度
		byte[] sizeBytes = DataUtil.int2byte1(size);
		System.arraycopy(sizeBytes, 0, cmdDataBytes, index, sizeBytes.length);
		index += sizeBytes.length;

		for (int i = 0; i < alarmConfigList.size(); i++) {
			Object[] data = alarmConfigList.get(i);
//			Logger.d(TAG, "setAlarmConfigList: " + data[0] + ", " + data[1] + ", " + data[2] + ", " + data[3]);
			// 告警类型
			byte[] typeBytes = DataUtil.int2byte1((int) (data[0]));
			System.arraycopy(typeBytes, 0, cmdDataBytes, index, typeLen);
			index += typeLen;
			// 告警级别
			byte[] levelBytes = DataUtil.int2byte1((int) (data[1]));
			System.arraycopy(levelBytes, 0, cmdDataBytes, index, levelLen);
			index += levelLen;
			// 告警比较符
			byte[] comparatorBytes = DataUtil.int2byte1((int) (data[2]));
			System.arraycopy(comparatorBytes, 0, cmdDataBytes, index, comparatorLen);
			index += comparatorLen;
			// 告警值
			byte[] valueBytes = DataUtil.int2byte2((int) (((double) data[3]) * 100));
			System.arraycopy(valueBytes, 0, cmdDataBytes, index, valueLen);
			index += valueLen;
		}

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新当前负载
	 *
	 * @param load 当前负载
	 * @return data
	 */
	public static byte[] setCurrentLoad(int load) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_CURRENT_LOAD};

		byte[] cmdDataBytes = DataUtil.int2byte2(load);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新机手登录状态
	 *
	 * @param loggedIn 是否登录成功
	 * @return data
	 */
	public static byte[] updateDriverLoginStatus(boolean loggedIn) {
		Logger.d(TAG, "updateDriverLoginStatus: " + loggedIn);
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {UPDATE_DRIVER_LOGIN_STATUS};
		byte[] cmdDataBytes = DataUtil.int2byte1(loggedIn ? 1 : 0);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置小车宽度
	 *
	 * @param width 平台配置的小车宽度
	 * @return data
	 */
	public static byte[] setTrolleyWidth(Double width) {
		Logger.d(TAG, "setTrolleyWidth: " + width);
		if (width == null || width < 0) {
			width = 0.0;
		}
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_TROLLEY_WIDTH};

		byte[] cmdDataBytes = DataUtil.int2byte2((int) (width * 100));
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置动臂俯仰角校准值
	 *
	 * @param inclinationOffset 校准值
	 * @return data
	 */
	public static byte[] setInclinationOffset(Double inclinationOffset) {
		Logger.d(TAG, "setInclinationOffset: " + inclinationOffset);
		if (inclinationOffset == null) {
			inclinationOffset = 0.0;
		}
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_INCLINATION_OFFSET};

		byte[] cmdDataBytes = DataUtil.signedInt2byte2((int) (inclinationOffset * 100));
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanyVariation(boolean on, int gear) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_VARIATION};
		byte[] cmdDataBytes = new byte[1 + 1 + 1];

		byte[] enabledBytes = DataUtil.int2byte1(on ? 0 : 1);
		int direction = 0;
		if (gear > 0) {
			direction = 1;
		} else if (gear < 0) {
			direction = 2;
		}
		byte[] directionBytes = DataUtil.int2byte1(direction);
		byte[] gearBytes = DataUtil.int2byte1(Math.abs(gear));
		System.arraycopy(enabledBytes, 0, cmdDataBytes, 0, enabledBytes.length);
		System.arraycopy(directionBytes, 0, cmdDataBytes, 1, directionBytes.length);
		System.arraycopy(gearBytes, 0, cmdDataBytes, 2, gearBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanySpin(boolean on, int gear) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_SPIN};
		byte[] cmdDataBytes = new byte[1 + 1 + 1];

		byte[] enabledBytes = DataUtil.int2byte1(on ? 0 : 1);
		int direction = 0;
		if (gear > 0) {
			direction = 1;
		} else if (gear < 0) {
			direction = 2;
		}
		byte[] directionBytes = DataUtil.int2byte1(direction);
		byte[] gearBytes = DataUtil.int2byte1(Math.abs(gear));
		System.arraycopy(enabledBytes, 0, cmdDataBytes, 0, enabledBytes.length);
		System.arraycopy(directionBytes, 0, cmdDataBytes, 1, directionBytes.length);
		System.arraycopy(gearBytes, 0, cmdDataBytes, 2, gearBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanyLifting(boolean on, int gear) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_LIFTING};
		byte[] cmdDataBytes = new byte[1 + 1 + 1];

		byte[] enabledBytes = DataUtil.int2byte1(on ? 0 : 1);
		int direction = 0;
		if (gear > 0) {
			direction = 1;
		} else if (gear < 0) {
			direction = 2;
		}
		byte[] directionBytes = DataUtil.int2byte1(direction);
		byte[] gearBytes = DataUtil.int2byte1(Math.abs(gear));
		System.arraycopy(enabledBytes, 0, cmdDataBytes, 0, enabledBytes.length);
		System.arraycopy(directionBytes, 0, cmdDataBytes, 1, directionBytes.length);
		System.arraycopy(gearBytes, 0, cmdDataBytes, 2, gearBytes.length);

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanySpinBreak(boolean on) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_SPIN_BREAK};

		byte[] cmdDataBytes = DataUtil.int2byte1(on ? 1 : 0);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanyReset(boolean on) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_RESET};

		byte[] cmdDataBytes = DataUtil.int2byte1(on ? 1 : 0);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanyPower(boolean on) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_POWER};

		byte[] cmdDataBytes = DataUtil.int2byte1(on ? 1 : 0);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] setSanyStop(boolean on) {
		byte[] cmdSet = {SANY_CONTROL_COMMAND};
		byte[] cmdIdBytes = {SANY_STOP};

		byte[] cmdDataBytes = DataUtil.int2byte1(on ? 1 : 0);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 请求OTA升级
	 *
	 * @param upgradeType 升级类型
	 * @return byte[]
	 */
	public static byte[] packRequestOta(byte upgradeType) {
		byte[] cmdSet = {OTA_CMD};
		byte[] cmdIdBytes = {OTA_START_UPGRADE};
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, new byte[]{upgradeType}));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 发送OTA文件信息
	 *
	 * @param upgradeType 升级类型
	 * @param fileSize    文件总大小
	 * @param sliceSize   每一分片大小
	 * @param md5         文件md5
	 * @return byte[]
	 */
	public static byte[] packOtaFileInfo(byte upgradeType, int fileSize, int sliceSize, byte[] md5) {
		byte[] cmdSet = {OTA_CMD};
		byte[] cmdIdBytes = {OTA_FILE_INFO};
		byte[] cmdDataBytes = new byte[1 + 4 + 2 + md5.length];
		cmdDataBytes[0] = upgradeType;
		byte[] sizeBytes = DataUtil.int2byte(fileSize);
		System.arraycopy(sizeBytes, 0, cmdDataBytes, 1, 4);
		byte[] countBytes = DataUtil.intToByte2(sliceSize);
		System.arraycopy(countBytes, 0, cmdDataBytes, 5, 2);
		System.arraycopy(md5, 0, cmdDataBytes, 7, md5.length);
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 发送OTA分包文件
	 *
	 * @param upgradeType  升级类型
	 * @param currentIndex 当前分包索引
	 * @param content      分包内容
	 * @return byte[]
	 */
	public static byte[] packSplitOtaFile(byte upgradeType, int currentIndex, byte[] content) {
		byte[] cmdSet = {OTA_CMD};
		byte[] cmdIdBytes = {OTA_SEND_FILE};
		byte[] cmdDataBytes = new byte[1 + 2 + content.length];
		cmdDataBytes[0] = upgradeType;
		byte[] indexBytes = DataUtil.int2byte2(currentIndex);
		System.arraycopy(indexBytes, 0, cmdDataBytes, 1, 2);
		System.arraycopy(content, 0, cmdDataBytes, 3, content.length);
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 配置缓停延迟
	 *
	 * @return byte[]
	 */
	public static byte[] configSlowdownDelay(int spin1,
																					 int spin2,
																					 int variation1,
																					 int variation2,
																					 int variation3,
																					 int variation4,
																					 int hook1,
																					 int hook2,
																					 int hook3,
																					 int hook4) {
		Logger.d(TAG, "configSlowdownDelay");
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {CONFIG_SLOWDOWN_DELAY};
		byte[] cmdDataBytes = new byte[10];
		cmdDataBytes[0] = (byte) (spin1 & 0xff);
		cmdDataBytes[1] = (byte) (spin2 & 0xff);
		cmdDataBytes[2] = (byte) (variation1 & 0xff);
		cmdDataBytes[3] = (byte) (variation2 & 0xff);
		cmdDataBytes[4] = (byte) (variation3 & 0xff);
		cmdDataBytes[5] = (byte) (variation4 & 0xff);
		cmdDataBytes[6] = (byte) (hook1 & 0xff);
		cmdDataBytes[7] = (byte) (hook2 & 0xff);
		cmdDataBytes[8] = (byte) (hook3 & 0xff);
		cmdDataBytes[9] = (byte) (hook4 & 0xff);
		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新雷达检测到的最近障碍物距离
	 *
	 * @param distance 距离
	 * @return byte[]
	 */
	public static byte[] updateRadarDistance(double distance) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_RADAR_DISTANCE};

		byte[] cmdDataBytes = DataUtil.int2byte2((int) (distance * 100));
		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 设置半自动吊运时的上升和下降高度
	 *
	 * @param up   上升高度
	 * @param down 下降高度
	 * @return byte[]
	 */
	public static byte[] setSemiAutoLiftingHeight(double up, double down) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SET_SEMIAUTOMATIC_HEIGHT};
		byte[] cmdDataBytes = DataUtil.byteMerger(DataUtil.int2byte2((int) (up * 100)), DataUtil.int2byte2((int) (down * 100)));

		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	public static byte[] startSemiAutomaticTask(double up, double down, GeoData targetLocation, GeoData tower, double armHeading, double jibAngle) {
		if (targetLocation == null) {
			return null;
		}
		if (tower == null) {
			tower = new GeoData(0, 0, 0);
		}
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {START_SEMIAUTOMATIC_TASK_REQ};
		byte[] upBytes = DataUtil.int2byte2((int) (up * 100));
		byte[] downBytes = DataUtil.int2byte2((int) (down * 100));
		byte[] latBytes = DataUtil.longToByteArray((long) (targetLocation.getLat() * Math.pow(10, 10)));
		byte[] lngBytes = DataUtil.longToByteArray((long) (targetLocation.getLng() * Math.pow(10, 10)));
		byte[] altBytes = DataUtil.int2byte((int) (targetLocation.getAlt() * 10_000));
		byte[] towerLatBytes = DataUtil.longToByteArray((long) (tower.getLat() * Math.pow(10, 10)));
		byte[] towerLngBytes = DataUtil.longToByteArray((long) (tower.getLng() * Math.pow(10, 10)));
		byte[] towerAltBytes = DataUtil.int2byte((int) (tower.getAlt() * 10_000));
		byte[] headingBytes = DataUtil.signedInt2byte2((int) (armHeading * 100));
		byte[] jibAngleBytes = DataUtil.signedInt2byte2((int) (jibAngle * 100));
		byte[] cmdDataBytes = DataUtil.byteMerger(upBytes,
			DataUtil.byteMerger(downBytes,
				DataUtil.byteMerger(latBytes,
					DataUtil.byteMerger(lngBytes,
						DataUtil.byteMerger(altBytes,
							DataUtil.byteMerger(towerLatBytes,
								DataUtil.byteMerger(towerLngBytes,
									DataUtil.byteMerger(towerAltBytes,
										DataUtil.byteMerger(headingBytes, jibAngleBytes)))))))));

		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新切断信号和风标指示灯信号
	 *
	 * @param cutoff  是否切断
	 * @param lightOn 是否灯亮
	 * @return byte[]
	 */
	public static byte[] updateCutoffAndLightSignal(boolean cutoff, boolean lightOn) {
		Logger.d(TAG, "updateCutoffAndLightSignal: cutoff -> " + cutoff + ", lightSignal -> " + lightOn);
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {UPDATE_CUTOFF_AND_LIGHT_SIGNAL};
		byte[] cmdDataBytes = new byte[]{(cutoff ? (byte) 0x01 : (byte) 0x00), (lightOn ? (byte) 0x01 : (byte) 0x00)};

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新气象站数据
	 *
	 * @param temperature      温度
	 * @param humidity         湿度
	 * @param windDirection    风向
	 * @param windSpeed        风速
	 * @param windSpeedAverage 2分钟平均风速
	 * @return byte[]
	 */
	public static byte[] updateWeatherData(double temperature, double humidity, int windDirection, double windSpeed, double windSpeedAverage) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {UPDATE_WEATHER_STATION_DATA};
		byte[] temperatureBytes = DataUtil.signedInt2byte2((int) (temperature * 10));
		byte[] humidityBytes = DataUtil.int2byte2((int) (humidity * 10));
		byte[] windDirectionBytes = DataUtil.int2byte2((windDirection));
		byte[] windSpeedBytes = DataUtil.int2byte2((int) (windSpeed * 10));
		byte[] windSpeedAverageBytes = DataUtil.int2byte2((int) (windSpeedAverage * 10));
		byte[] cmdDataBytes = DataUtil.byteMerger(temperatureBytes,
			DataUtil.byteMerger(humidityBytes,
				DataUtil.byteMerger(windDirectionBytes,
					DataUtil.byteMerger(windSpeedBytes, windSpeedAverageBytes))));

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 配置塔吊类型
	 *
	 * @param type 0-永茂；1-三一
	 * @return byte[]
	 */
	public static byte[] configTowerType(int type) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {UPDATE_TOWER_TYPE};
		byte[] cmdDataBytes = new byte[]{(byte) type};

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, cmdDataBytes));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 给ecu发复位消息模拟出现看门狗
	 */
	public static byte[] mockEcuRestart() {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {MOCK_ECU_RESTART};

		byte[] reqDataDM =
			DataUtil.byteMerger(cmdSet, cmdIdBytes);
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新333告警状态
	 *
	 * @param limit 是否需要限制控制
	 */
	public static byte[] update333Alarm(boolean limit) {
		byte[] reqDataDM = new byte[]{DISCRETE_COMMAND, UPDATE_333_ALARM, (byte) (limit ? 1 : 0)};
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 透传永茂平头塔吊数据到ecu
	 *
	 * @param data data
	 * @return byte[]
	 */
	public static byte[] sendYongMaoData(byte[] data) {
		byte[] cmdSet = {DISCRETE_COMMAND};
		byte[] cmdIdBytes = {SEND_YONGMAO_DATA};

		byte[] reqDataDM = DataUtil.byteMerger(cmdSet, DataUtil.byteMerger(cmdIdBytes, data));
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqDataDM.length - 1));
		formatter.setData(reqDataDM);
		return formatter.getFormattedData();
	}

	/**
	 * 更新半自动任务状态
	 *
	 * @param status 1:继续 2:结束
	 * @return byte[]
	 */
	public static byte[] updateSemiautomaticTask(int status) {
		byte[] reqData = new byte[]{DISCRETE_COMMAND, UPDATE_SEMIAUTOMATIC_TASK, (byte) status};
		TowerDataFormatter formatter = new TowerDataFormatter();
		formatter.setDataLength(DataUtil.int2byte2(reqData.length - 1));
		formatter.setData(reqData);
		return formatter.getFormattedData();
	}

}
