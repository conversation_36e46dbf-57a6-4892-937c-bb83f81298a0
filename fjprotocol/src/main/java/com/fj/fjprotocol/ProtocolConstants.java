package com.fj.fjprotocol;

/**
 * ECU协议相关常量定义
 *
 * <AUTHOR>
 */
public class ProtocolConstants {
	/**
	 * 心跳包
	 */
	public static final byte HEARTBEAT = (byte) 0xff;
	/**
	 * 离散指令
	 */
	public static final byte DISCRETE_COMMAND = (byte) 0x04;
	/**
	 * 查询ECU版本号
	 */
	public static final byte ECU_VERSION = (byte) 0x21;
	/**
	 * ECU实时状态上报
	 */
	public static final byte ECU_REALTIME_STATUS = (byte) 0xa1;
	/**
	 * 座椅按键状态上报
	 */
	public static final byte ECU_BUTTON_STATUS = (byte) 0xa2;
	/**
	 * ecu请求开始半自动吊运
	 */
	public static final byte ECU_REQUEST_SEMIAUTOMATIC_START = (byte) 0xa3;
	/**
	 * ecu重启
	 */
	public static final byte ECU_RESTART = (byte) 0xa4;
	/**
	 * ECU日志上报
	 */
	public static final byte ECU_LOG = (byte) 0xaf;
	/**
	 * 设置工作模式
	 */
	public static final byte SET_WORKING_MODE = (byte) 0x02;
	/**
	 * 设置A点经纬高
	 */
	public static final byte SET_LOCATION_A = (byte) 0x04;
	/**
	 * 设置B点经纬高
	 */
	public static final byte SET_LOCATION_B = (byte) 0x05;
	/**
	 * 设置C点经纬高
	 */
	public static final byte SET_LOCATION_C = (byte) 0x06;
	/**
	 * 设置吊臂长度及远近端安全距离
	 */
	public static final byte SET_ARM = (byte) 0x07;
	/**
	 * 设置塔吊高度及安全距离
	 */
	public static final byte SET_TOWER = (byte) 0x08;
	/**
	 * 设置台架模式
	 */
	public static final byte SET_TEST_MODE = (byte) 0x09;
	/**
	 * 设置障碍物8个经纬高
	 */
	public static final byte SET_OBSTACLE_POINTS = (byte) 0x11;
	/**
	 * 设置吊物尺寸
	 */
	public static final byte SET_CARGO_SIZE = (byte) 0x12;
	/**
	 * 设置避障预警
	 */
	public static final byte SET_OBSTACLE_WARN = (byte) 0x13;
	/**
	 * 设置风速预警
	 */
	public static final byte SET_WIND_SPEED_WARN = (byte) 0x14;
	/**
	 * 设置动态负载预警，包含长度和对应负载
	 */
	public static final byte SET_LOAD_WARN_TABLE = (byte) 0x15;
	/**
	 * 更新当前负载，需要周期下发
	 */
	public static final byte SET_CURRENT_LOAD = (byte) 0x16;
	/**
	 * 设置预警规则
	 */
	public static final byte SET_ALARM_CONFIG = (byte) 0x17;
	/**
	 * 更新雷达检测最近障碍物距离
	 */
	public static final byte SET_RADAR_DISTANCE = (byte) 0x18;
	/**
	 * 设置小车宽度
	 */
	public static final byte SET_TROLLEY_WIDTH = (byte) 0x19;
	/**
	 * 设置动臂俯仰角校准值
	 */
	public static final byte SET_INCLINATION_OFFSET = (byte) 0x20;
	/**
	 * 设置半自动吊运上升和下降高度
	 */
	public static final byte SET_SEMIAUTOMATIC_HEIGHT = (byte) 0x22;
	/**
	 * 下发轨迹信息
	 */
	public static final byte SEND_HISTORY_POINTS = (byte) 0x31;
	/**
	 * 接收轨迹信息情况，ECU回复
	 */
	public static final byte SEND_HISTORY_RESPONSE = (byte) 0x32;
	/**
	 * 机手登录状态
	 */
	public static final byte UPDATE_DRIVER_LOGIN_STATUS = (byte) 0x33;
	/**
	 * 请求开始半自动任务
	 */
	public static final byte START_SEMIAUTOMATIC_TASK_REQ = (byte) 0x36;
	/**
	 * 开始半自动任务响应
	 */
	public static final byte START_SEMIAUTOMATIC_TASK_RESP = (byte) 0x37;
	/**
	 * 设置塔吊类型
	 */
	public static final byte UPDATE_TOWER_TYPE = (byte) 0x39;
	/**
	 * 更新切断和指示灯信号
	 */
	public static final byte UPDATE_CUTOFF_AND_LIGHT_SIGNAL = (byte) 0x3a;
	/**
	 * 更新气象站数据
	 */
	public static final byte UPDATE_WEATHER_STATION_DATA = (byte) 0x3b;
	/**
	 * 更新333告警状态
	 */
	public static final byte UPDATE_333_ALARM = (byte) 0x3c;
	/**
	 * 永茂平头塔吊数据透传
	 */
	public static final byte SEND_YONGMAO_DATA = (byte) 0x3e;
	/**
	 * 不同类型塔吊调试状态上报
	 */
	public static final byte DEBUG_STATUS_REPORT = (byte) 0xfd;
	/**
	 * 三一塔吊数据上报
	 */
	public static final byte SANY_STATUS_REPORT_TYPE = (byte) 0xa2;
	/**
	 * 徐工塔吊数据上报
	 */
	public static final byte XCMD_STATUS_REPORT_TYPE = (byte) 0xa3;
	/**
	 * 永茂平头塔吊数据上报
	 */
	public static final byte YONGMAO_STATUS_REPORT_TYPE = (byte) 0xa4;
	/**
	 * SANY塔吊调试指令
	 */
	public static final byte SANY_CONTROL_COMMAND = (byte) 0xfe;
	/**
	 * 变幅通道指令
	 */
	public static final byte SANY_VARIATION = (byte) 0x01;
	/**
	 * 回转通道指令
	 */
	public static final byte SANY_SPIN = (byte) 0x02;
	/**
	 * 起升通道指令
	 */
	public static final byte SANY_LIFTING = (byte) 0x03;
	/**
	 * 回转刹车指令
	 */
	public static final byte SANY_SPIN_BREAK = (byte) 0x04;
	/**
	 * 故障复位指令
	 */
	public static final byte SANY_RESET = (byte) 0x05;
	/**
	 * 动力电源指令
	 */
	public static final byte SANY_POWER = (byte) 0x06;
	/**
	 * 急停指令
	 */
	public static final byte SANY_STOP = (byte) 0x07;
	/**
	 * OTA相关指令
	 */
	public static final byte OTA_CMD = 0x1f;
	/**
	 * app请求开始升级 -> ecu响应
	 */
	public static final byte OTA_START_UPGRADE = 0x02;
	/**
	 * ecu请求文件信息 -> app发送文件信息
	 */
	public static final byte OTA_FILE_INFO = 0x05;
	/**
	 * 发送分片文件 -> ecu请求下一包
	 */
	public static final byte OTA_SEND_FILE = 0x03;
	/**
	 * ecu上报OTA结果
	 */
	public static final byte OTA_RESULT = 0x04;
	/**
	 * 下发缓停调试参数
	 */
	public static final byte CONFIG_SLOWDOWN_DELAY = 0x34;
	/**
	 * 模拟ecu重启
	 */
	public static final byte MOCK_ECU_RESTART = (byte) 0xff;
	/**
	 * ecu需要保存的字符串日志
	 */
	public static final byte ECU_STR_LOG = (byte) 0xff;
	/**
	 * 下发半自动任务指令
	 */
	public static final byte UPDATE_SEMIAUTOMATIC_TASK = (byte) 0x3D;
	/**
	 * 设置编码器当前位置为零点
	 */
	public static final byte RESET_ABSOLUTE_ENCODER = (byte) 0x40;
	/**
	 * 设置编码器值递增方向
	 */
	public static final byte SET_ABSOLUTE_ENCODER_DIRECTION = (byte) 0x41;

}
