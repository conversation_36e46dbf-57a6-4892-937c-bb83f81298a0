package com.fjd.app.common.tcp.client;

import com.blankj.utilcode.util.CloseUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.fjd.app.common.util.DataUtil;
import com.fjd.app.common.util.LogUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TCP客户端
 *
 * <AUTHOR>
 */
public class TcpClient {
	private static final String TAG = "TcpClient";
	private static final int DEFAULT_TIMEOUT = 800;
	private static final int BUFFER_SIZE = 1024;
	private static final int STATUS_DISCONNECTED = 0;
	private static final int STATUS_CONNECTING = 1;
	private static final int STATUS_CONNECTED = 2;
	private final AtomicInteger status = new AtomicInteger(STATUS_DISCONNECTED);
	private final String ip;
	private final int port;
	private final Callback callback;
	private final int timeout;
	private OutputStream out;
	private InputStream in;
	private Socket socket;

	public TcpClient(String ip, int port, Callback callback) {
		this(ip, port, DEFAULT_TIMEOUT, callback);
	}

	public TcpClient(String ip, int port, int timeout, Callback callback) {
		this.ip = ip;
		this.port = port;
		this.timeout = timeout;
		this.callback = callback;
	}

	public void connect() {
		if (status.get() == STATUS_CONNECTING) {
			LogUtils.w(TAG, "connect: already connecting");
			return;
		}
		new Thread(() -> {
			try {
				status.set(STATUS_CONNECTING);
				CloseUtils.closeIOQuietly(socket);
				socket = new Socket();
				socket.connect(new InetSocketAddress(ip, port), timeout);
				status.set(STATUS_CONNECTED);
				LogUtils.d(TAG, "connected to " + socket.toString());
				in = socket.getInputStream();
				out = socket.getOutputStream();
				ThreadUtils.runOnUiThread(() -> {
					if (callback != null) {
						callback.onConnectStatusChanged(status.get() == STATUS_CONNECTED);
					}
				});
				byte[] buffer = new byte[BUFFER_SIZE];
				for (int len; (len = in.read(buffer)) != -1; ) {
					byte[] data = DataUtil.subBytes(buffer, 0, len);
					ThreadUtils.runOnUiThread(() -> {
//						LogUtils.d(TAG, "receive: " + DataUtil.byte2hex(data));
						if (callback != null) {
							callback.onDataReceived(data);
						}
					});
				}
			} catch (IOException e) {
				LogUtils.e(TAG, "connect exception: " + e.getMessage());
			} finally {
				disconnect();
			}
		}).start();
	}

	public void disconnect() {
		if (status.get() == STATUS_DISCONNECTED) {
			LogUtils.w(TAG, "disconnect: already disconnected");
			return;
		}
		LogUtils.d(TAG, "disconnect: " + (socket == null ? "" : socket.toString()));
		CloseUtils.closeIOQuietly(in, out, socket);
		status.set(STATUS_DISCONNECTED);
		ThreadUtils.runOnUiThread(() -> {
			if (callback != null) {
				callback.onConnectStatusChanged(status.get() == STATUS_CONNECTED);
			}
		});
	}

	public void send(byte[] data) {
		new Thread(() -> {
			if (socket == null
				|| socket.isClosed()
				|| out == null
				|| data == null
				|| data.length == 0) {
				return;
			}
			try {
//				LogUtils.d(TAG, "send: " + DataUtil.byte2hex(data));
				out.write(data);
			} catch (IOException e) {
				LogUtils.e(TAG, "send exception: " + (socket == null ? "" : socket.toString()) + " -> " + e.getMessage());
			}
		}).start();
	}

	public interface Callback {
		void onConnectStatusChanged(boolean connected);

		void onDataReceived(byte[] data);
	}
}
