package com.fj.towercontrol.consts;

import android.os.Environment;

import java.io.File;
import java.net.URI;

/**
 * App相关常量
 *
 * <AUTHOR>
 */
public class AppConstants {
	/**
	 * ota升级文件下载根目录
	 */
	public static final String OTA_BASE_DIR = Environment.getExternalStorageDirectory().getAbsolutePath() + File.separator + "FJDynamics" + File.separator + "Tower" + File.separator + "OTA";
	/**
	 * app ota文件目录
	 */
	public static final String OTA_APP_DIR = OTA_BASE_DIR + File.separator + "app";
	/**
	 * ecu ota文件目录
	 */
	public static final String OTA_ECU_DIR = OTA_BASE_DIR + File.separator + "ecu";
	/**
	 * 默认大屏ws服务地址
	 */
	public static final URI DEFAULT_LARGE_SCREEN_URL = URI.create("ws://192.168.0.199:8070");
	/**
	 * 默认智能吊钩称重和电池485模块转以太网地址
	 */
	public static final URI DEFAULT_HOOK_URL = URI.create("tcp://192.168.0.15:51001");
	/**
	 * 默认永茂塔机地址
	 */
	public static final URI DEFAULT_YONGMAO_CRANE_URL = URI.create("tcp://192.168.0.253:51001");
	/**
	 * 默认塔上钥匙开关IO模块地址
	 */
	public static final URI DEFAULT_TOWER_TOP_IO_URL = URI.create("tcp://192.168.0.128:51001");
	/**
	 * 默认灯光告警IO模块地址
	 */
	public static final URI DEFAULT_ALARM_IO_URL = URI.create("tcp://192.168.0.130:51001");
	/**
	 * 默认声音告警IO模块地址
	 */
	public static final URI DEFAULT_VOICE_ALARM_URL = URI.create("tcp://192.168.0.131:51001");
	/**
	 * 默认气象485模块站地址
	 */
	public static final URI DEFAULT_WEATHER_STATION_URL = URI.create("tcp://192.168.0.7:51001");
	/**
	 * 默认舵机485模块地址
	 */
	public static final URI DEFAULT_MOTOR_URL = URI.create("tcp://192.168.0.14:51001");
	/**
	 * 汽车吊告警模块地址
	 */
	public static final URI DEFAULT_CAR_CRANE_URL = URI.create("tcp://192.168.0.151:51001");
	/**
	 * 永茂485转以太网模块地址
	 */
	public static final URI DEFAULT_YONGMAO_485_URL = URI.create("tcp://192.168.0.133:51001");
	/**
	 * 默认雷电预警485转以太网模块地址
	 */
	public static final URI DEFAULT_LIGHTNING_ALERT_URL = URI.create("tcp://192.168.0.188:51001");
}
