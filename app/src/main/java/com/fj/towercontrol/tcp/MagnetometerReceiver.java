package com.fj.towercontrol.tcp;

//import com.fj.towercontrol.data.entity.MagnetometerInfo;
//import com.fj.towercontrol.event.MessageEvent;
//import com.fjd.app.common.tcp.TcpServer;
//import com.fjd.app.common.util.DataUtil;
//
//import org.greenrobot.eventbus.EventBus;

/**
 * 地磁仪数据接受服务
 *
 * <AUTHOR>
 */
public class MagnetometerReceiver {
//	private static final String TAG = "MagnetometerReceiver";
//	private static final byte[] HEAD = new byte[]{(byte) 0x08, (byte) 0x00, (byte) 0x00};
//	private static final int PACKET_SIZE = 13;
//	private final TcpServer tcpServer;
//	private byte[] buffer = new byte[0];
//	private float roll;
//	private float pitch;
//	private float yaw;
//	private long uiUpdateTimestamp;
//
//	private MagnetometerReceiver() {
//		tcpServer = new TcpServer(8890, this::spiltData);
//	}
//
//	private void spiltData(byte[] data) {
////		Log.d(TAG, "receive data: " + data.length);
//		buffer = DataUtil.byteMerger(buffer, data);
//		while (buffer.length >= PACKET_SIZE) {
//			int headIndex = findHead(buffer);
//			if (headIndex < 0) {
//				// 未找到帧头
//				return;
//			}
//			int tailIndex = headIndex + PACKET_SIZE - 1;
//			if (tailIndex > buffer.length - 1) {
//				// 当前数据不完整，等待下一包
//				return;
//			}
//			int clipStartIndex;
//			// 找到帧尾，回调这一包数据
//			byte[] frameData = DataUtil.subBytes(buffer, headIndex, PACKET_SIZE);
//			parseData(frameData);
//			// 校验通过裁剪帧
//			clipStartIndex = tailIndex + 1;
//			buffer = DataUtil.subBytes(buffer, clipStartIndex, buffer.length - clipStartIndex);
//		}
//	}
//
//	private int findHead(byte[] data) {
//		for (int i = 0; i < data.length - 2; ++i) {
//			if (data[i] == HEAD[0] && data[i + 1] == HEAD[1] && data[i + 2] == HEAD[2]) {
//				return i;
//			}
//		}
//		return -1;
//	}
//
//	private void parseData(byte[] frameData) {
////		Log.e(TAG, "parseData -> " + DataUtil.byte2hex(frameData));
//		if (frameData[4] == (byte) 0x65) {
//			//解析pitch、roll
//			byte[] rollBytes = DataUtil.subBytes(frameData, 5, 4);
//			roll = DataUtil.byte2Float(rollBytes);
//			byte[] pitchBytes = DataUtil.subBytes(frameData, 9, 4);
//			pitch = DataUtil.byte2Float(pitchBytes);
//		} else if (frameData[4] == (byte) 0x66) {
//			//解析yaw
//			byte[] yawBytes = DataUtil.subBytes(frameData, 5, 4);
//			yaw = DataUtil.byte2Float(yawBytes);
//		}
//		long currentTimeMillis = System.currentTimeMillis();
//		if (currentTimeMillis - uiUpdateTimestamp >= 1_000) {
//			//1hz频率更新界面数据
//			MagnetometerInfo magnetometerInfo = new MagnetometerInfo(roll, pitch, yaw);
//			EventBus.getDefault().post(new MessageEvent(MessageEvent.MAGNETOMETER_UPDATE, magnetometerInfo));
//			uiUpdateTimestamp = currentTimeMillis;
//		}
//	}
//
//	public void start() {
//		tcpServer.start();
//	}
//
//	public void stop() {
//		tcpServer.stop();
//	}
//
//	private static class SingletonHolder {
//		private static final MagnetometerReceiver INSTANCE = new MagnetometerReceiver();
//	}
//
//	public static MagnetometerReceiver getInstance() {
//		return MagnetometerReceiver.SingletonHolder.INSTANCE;
//	}
}
