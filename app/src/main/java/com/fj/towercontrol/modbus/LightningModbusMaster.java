package com.fj.towercontrol.modbus;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.fj.towercontrol.data.entity.LightningData;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fj.towercontrol.util.MemoryStore;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import org.greenrobot.eventbus.EventBus;

import java.net.URI;
import java.nio.ByteBuffer;

/**
 * 雷电预警模块modbus通信
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
public class LightningModbusMaster {
	private static final String TAG = "LightningModbusMaster";
	/**
	 * 尝试重连消息
	 */
	private static final int MSG_RETRY_CONNECT = 10001;
	/**
	 * 重置告警状态消息
	 */
	private static final int MSG_READ_NEXT_DATA = 10002;
	private static final int SLAVE_ID = 0x01;
	private static final int START_ADDRESS = 0x00;
	private static final int READ_REGISTER_COUNT = 5;
	private static final long READ_REGISTER_INTERVAL = 10_000L;
	private final Handler handler;
	private final CustomModbusReq modbusReq;

	private LightningModbusMaster() {
		modbusReq = new CustomModbusReq();
		handler = new Handler(Looper.getMainLooper()) {
			@Override
			public void handleMessage(@NonNull Message msg) {
				if (msg.what == MSG_RETRY_CONNECT) {
					initModbus();
				} else if (msg.what == MSG_READ_NEXT_DATA) {
					readLightningData();
				}
			}
		};
	}

	private static class SingletonHolder {
		private static final LightningModbusMaster INSTANCE = new LightningModbusMaster();
	}

	public static LightningModbusMaster getInstance() {
		return LightningModbusMaster.SingletonHolder.INSTANCE;
	}

	public void start() {
		handler.postDelayed(this::initModbus, 2_000);
	}

	public void stop() {
		modbusReq.destroy();
		handler.removeCallbacksAndMessages(null);
	}

	private void initModbus() {
		modbusReq.destroy();
		URI config = MemoryStore.getInstance().getSystemConfig().getLightningAlertUrl();
		ModbusParam modbusParam = new ModbusParam()
			.setHost(config.getHost())
			.setPort(config.getPort())
			.setEncapsulated(true)
			.setKeepAlive(true)
			.setTimeout(800)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
					readLightningData();
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_LIGHTNING_DATA_UPDATE, null));
					handler.sendEmptyMessageDelayed(MSG_RETRY_CONNECT, 2_000);
				}
			});
	}

	private void readLightningData() {
		modbusReq.readHoldingRegistersRaw(new OnRequestBack<>() {
			@Override
			public void onSuccess(byte[] data) {
				if (data == null || data.length < READ_REGISTER_COUNT * 2) {
					Logger.w(TAG, "readLightningData: invalid data");
					handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
					return;
				}
				Log.d(TAG, "readLightningData onSuccess: " + DataUtil.byte2hex(data));
				ByteBuffer buffer = ByteBuffer.wrap(data);
				float electricFieldStrength = buffer.getFloat();
				float motorSpeed = buffer.getFloat();
				int motorErrorCode = Short.toUnsignedInt(buffer.getShort());
				Logger.d(TAG, "electricFieldStrength: " + electricFieldStrength + ", motorSpeed: " + motorSpeed + ", motorErrorCode: " + motorErrorCode);
				LightningData lightningData = new LightningData(electricFieldStrength, motorSpeed, motorErrorCode);
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_LIGHTNING_DATA_UPDATE, lightningData));
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
			}

			@Override
			public void onFailed(String s) {
				Logger.e(TAG, "readLightningData onFailed: " + s);
				EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_LIGHTNING_DATA_UPDATE, null));
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
			}
		}, SLAVE_ID, START_ADDRESS, READ_REGISTER_COUNT);
	}

}
