package com.fj.towercontrol.modbus;

//import android.os.Handler;
//import android.os.Looper;
//import android.os.Message;
//import android.util.Log;
//
//import androidx.annotation.NonNull;
//
//import com.fj.towercontrol.data.entity.AlarmType;
//import com.fj.towercontrol.util.CustomModbusReq;
//import com.fj.towercontrol.util.MemoryStore;
//import com.fjdynamics.app.logger.Logger;
//import com.zgkxzx.modbus4And.requset.ModbusParam;
//import com.zgkxzx.modbus4And.requset.OnRequestBack;
//
//import java.net.URI;

/**
 * 汽车吊告警IO控制类
 *
 * <AUTHOR>
 */
public class CarCraneModbusReader {
//	private static final String TAG = "CarCraneModbusReader";
//	/**
//	 * 尝试重连消息
//	 */
//	private static final int MSG_RETRY_CONNECT = 10001;
//	/**
//	 * 重置告警状态消息
//	 */
//	private static final int MSG_RESET_STATUS = 10002;
//	private static final int SLAVE_ID = 0x11;
//	private static final int START_ADDRESS = 0x00;
//	private final Handler handler;
//	private final CustomModbusReq modbusReq;
//
//	public void start() {
//		handler.postDelayed(this::initModbus, 2_000);
//	}
//
//	public void stop() {
//		modbusReq.destroy();
//		handler.removeCallbacksAndMessages(null);
//	}
//
//	private void initModbus() {
//		modbusReq.destroy();
//		URI carCraneUrl = MemoryStore.getInstance().getSystemConfig().getCarCraneUrl();
//		ModbusParam modbusParam = new ModbusParam()
//			.setHost(carCraneUrl.getHost())
//			.setPort(51001)
//			.setEncapsulated(true)
//			.setKeepAlive(true)
//			.setTimeout(800)
//			.setRetries(0);
//		modbusReq.setParam(modbusParam)
//			.init(new OnRequestBack<String>() {
//				@Override
//				public void onSuccess(String s) {
//					Log.d(TAG, "initModbus onSuccess: " + s);
//					handler.removeMessages(MSG_RETRY_CONNECT);
//				}
//
//				@Override
//				public void onFailed(String msg) {
//					Logger.e(TAG, "initModbus onFailed: " + msg);
//					handler.sendEmptyMessageDelayed(MSG_RETRY_CONNECT, 2_000);
//				}
//			});
//	}
//
//	public void controlIO(AlarmType alarmType) {
//		boolean io1;//橙色灯光告警
//		boolean io2;//红色声光告警
//		if (alarmType == AlarmType.Normal) {
//			io1 = false;
//			io2 = false;
//		} else if (alarmType == AlarmType.Warning) {
//			io1 = true;
//			io2 = false;
//		} else {
//			io1 = false;
//			io2 = true;
//		}
//		modbusReq.writeCoils(new OnRequestBack<String>() {
//			@Override
//			public void onSuccess(String s) {
//				Log.d(TAG, "writeCoils onSuccess: io1 -> " + io1 + ", io2 -> " + io2);
//				handler.removeCallbacksAndMessages(null);
//				if (io1 || io2) {
//					handler.sendEmptyMessageDelayed(MSG_RESET_STATUS, 5_000);
//				}
//			}
//
//			@Override
//			public void onFailed(String s) {
//				Log.e(TAG, "writeCoils onFailed: " + s);
//			}
//		}, SLAVE_ID, START_ADDRESS, new boolean[]{io1, io2});
//	}
//
//	private CarCraneModbusReader() {
//		modbusReq = new CustomModbusReq();
//		handler = new Handler(Looper.getMainLooper()) {
//			@Override
//			public void handleMessage(@NonNull Message msg) {
//				super.handleMessage(msg);
//				if (msg.what == MSG_RETRY_CONNECT) {
//					initModbus();
//				} else if (msg.what == MSG_RESET_STATUS) {
//					controlIO(AlarmType.Normal);
//				}
//			}
//		};
//	}
//
//	private static class SingletonHolder {
//		private static final CarCraneModbusReader INSTANCE = new CarCraneModbusReader();
//	}
//
//	public static CarCraneModbusReader getInstance() {
//		return CarCraneModbusReader.SingletonHolder.INSTANCE;
//	}
}
