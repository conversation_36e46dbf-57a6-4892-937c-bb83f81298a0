package com.fj.towercontrol.modbus;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.fj.towercontrol.data.entity.RopeDetectionData;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import org.greenrobot.eventbus.EventBus;

import java.io.EOFException;

import okio.Buffer;

/**
 * 缆绳检测模块modbus通信
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
public class RopeDetectionModbusMaster {
	private static final String TAG = "RopeDetectionModbusMaster";
	/**
	 * 尝试重连消息
	 */
	private static final int MSG_RETRY_CONNECT = 10001;
	/**
	 * 重置告警状态消息
	 */
	private static final int MSG_READ_NEXT_DATA = 10002;
	private static final int SLAVE_ID = 0x01;
	private static final int START_ADDRESS = 0x01;
	private static final int READ_REGISTER_COUNT = 5;
	private static final long READ_REGISTER_INTERVAL = 500L;
	private final Handler handler;
	private final CustomModbusReq modbusReq;
	private final Buffer buffer = new Buffer();

	private RopeDetectionModbusMaster() {
		modbusReq = new CustomModbusReq();
		handler = new Handler(Looper.getMainLooper()) {
			@Override
			public void handleMessage(@NonNull Message msg) {
				if (msg.what == MSG_RETRY_CONNECT) {
					initModbus();
				} else if (msg.what == MSG_READ_NEXT_DATA) {
					readRopeDetectionData();
				}
			}
		};
	}

	private static class SingletonHolder {
		private static final RopeDetectionModbusMaster INSTANCE = new RopeDetectionModbusMaster();
	}

	public static RopeDetectionModbusMaster getInstance() {
		return RopeDetectionModbusMaster.SingletonHolder.INSTANCE;
	}

	public void start() {
		handler.postDelayed(this::initModbus, 2_000);
	}

	public void stop() {
		modbusReq.destroy();
		handler.removeCallbacksAndMessages(null);
	}

	private void initModbus() {
		modbusReq.destroy();
		ModbusParam modbusParam = new ModbusParam()
			.setHost("*************")
			.setPort(51001)
			.setEncapsulated(true)
			.setKeepAlive(true)
			.setTimeout(800)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
					readRopeDetectionData();
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					handler.sendEmptyMessageDelayed(MSG_RETRY_CONNECT, 2_000);
				}
			});
	}

	private void readRopeDetectionData() {
		modbusReq.readInputRegisters(new OnRequestBack<>() {
			@Override
			public void onSuccess(byte[] data) {
				if (data == null || data.length < READ_REGISTER_COUNT * 2) {
					Logger.w(TAG, "readRopeDetectionData: invalid data");
					handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
					return;
				}
				Log.d(TAG, "readRopeDetectionData onSuccess: " + DataUtil.byte2hex(data));
				buffer.clear();
				buffer.write(data);
				try {
					short faultCode = buffer.readShort();
					short faultLevel = buffer.readShort();
					short speed = buffer.readShort();
					short detectLength = buffer.readShort();
					short position = buffer.readShort();
					Logger.d(TAG, "faultCode: " + faultCode + ", faultLevel: " + faultLevel + ", speed: " + speed + ", detectLength: " + detectLength + ", position: " + position);
					RopeDetectionData ropeDetectionData = new RopeDetectionData(faultCode, faultLevel, speed, detectLength, position);
					EventBus.getDefault().post(new MessageEvent(MessageEvent.CODE_ROPE_DETECTION_DATA_UPDATE, ropeDetectionData));
				} catch (EOFException e) {
					Logger.e(TAG, "readRopeDetectionData error: " + e.getMessage(), e);
				}
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
			}

			@Override
			public void onFailed(String s) {
				Logger.e(TAG, "readRopeDetectionData failed: " + s);
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
			}
		}, SLAVE_ID, START_ADDRESS, READ_REGISTER_COUNT);
	}

}
