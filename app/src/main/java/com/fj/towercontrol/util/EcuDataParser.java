package com.fj.towercontrol.util;

import com.fj.towercontrol.data.entity.EcuLog;
import com.fjd.app.common.util.DataUtil;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.Collections;
import java.util.List;

/**
 * Ecu数据解析类
 *
 * <AUTHOR>
 */
public class EcuDataParser {

	private static final String TAG = "EcuDataParser";

	/**
	 * 解析ecu版本号
	 *
	 * @param data data
	 * @return 版本号，比如1.2.3.4
	 */
	public static String parseVersion(byte[] data) {
		String ecuVersion =
			String.format(
				"%s.%s.%s.%s",
				data[0] & 0xff, data[1] & 0xff, data[2] & 0xff, data[3] & 0xff);
		return ecuVersion;
	}

	/**
	 * 解析ecu日志上报
	 *
	 * @param data data
	 * @return ecu上报的日志
	 */
	public static EcuLog parseLog(byte[] data) {
		EcuLog ecuLog = EcuLog.parse(data);
		return ecuLog;
	}

//	/**
//	 * 解析ecu实时状态上报
//	 *
//	 * @param data data
//	 * @return ecu实时状态
//	 */
//	public static EcuRealtimeStatus parseEcuRealtimeStatus(byte[] data) {
//		EcuRealtimeStatus ecuRealtimeStatus = new EcuRealtimeStatus(data);
//		return ecuRealtimeStatus;
//	}

	/**
	 * 遥控使能按钮解析
	 *
	 * @param bits ecu上报的遥控使能按钮数据
	 * @return 按位解析后的结果
	 */
	public static List<Integer> parseIntegerBits(int bits) {
		List<Integer> remoteFunctionList = new ArrayList<>();
		String remoteFunctionBinaryStr = Integer.toBinaryString(bits);
		String reversedStr = reverseStr(remoteFunctionBinaryStr);
		for (int i = 0; i < reversedStr.length(); i++) {
			if (reversedStr.charAt(i) == '1') {
				remoteFunctionList.add(i + 1);
			}
		}
		return remoteFunctionList;
	}

	/**
	 * 解析指令
	 *
	 * @param bits 指令
	 * @return bit结果
	 */
	public static List<Integer> parseLongBits(long bits) {
		List<Integer> bitList = new ArrayList<>();
		String errBinaryStr = Long.toBinaryString(bits);
		// 从低位到高位解析错误码，先反转二进制字符串，然后从头开始解析
		String reversedStr = reverseStr(errBinaryStr);
		for (int i = 0; i < reversedStr.length(); i++) {
			if (reversedStr.charAt(i) == '1') {
				// 如果状态是1，将错误位添加到错误码集合里，平台定义的错误码对应的是index+1
				bitList.add(i + 1);
			}
		}
		return bitList;
	}


	/**
	 * 双指针法反转字符串 时间复杂度：O(N) 空间复杂度：O(1)
	 *
	 * @param str 待反转的字符串
	 * @return 反转后的字符串
	 */
	private static String reverseStr(String str) {
		char[] s = str.toCharArray();
		int n = s.length;
		for (int left = 0, right = n - 1; left < right; ++left, --right) {
			char tmp = s[left];
			s[left] = s[right];
			s[right] = tmp;
		}
		return String.valueOf(s);
	}

	/**
	 * 解析ecu上报的座椅按钮状态
	 *
	 * @param data data
	 * @return 按下的按键集合
	 */
	public static List<Integer> parseEcuButtonStatus(byte[] data) {
		try {
			List<Integer> instruction = parseLongBits(DataUtil.unsigned4BytesToInt(DataUtil.subBytes(data, 0, 4), 0));
			List<Integer> buttonStatus = parseIntegerBits(data[4] & 0xff);
			return convertButtons(instruction, buttonStatus);
		} catch (Exception e) {
			return Collections.emptyList();
		}
	}

	private static List<Integer> convertButtons(List<Integer> instruction, List<Integer> buttonStatus) {
		List<Integer> buttons = new ArrayList<>();
		if (instruction == null || buttonStatus == null) {
			return buttons;
		}
		if (instruction.contains(26)) {
			//左-左上绿
			buttons.add(1);
		}
		if (buttonStatus.contains(6)) {
			//左-右上红
			buttons.add(2);
		}
		if (buttonStatus.contains(7)) {
			//左-左下绿
			buttons.add(3);
		}
		if (buttonStatus.contains(8)) {
			//左-右下红
			buttons.add(4);
		}
		if (instruction.contains(2)) {
			//右-左上绿
			buttons.add(5);
		}
		if (buttonStatus.contains(2)) {
			//右-右上红
			buttons.add(6);
		}
		if (buttonStatus.contains(3)) {
			//右-左下绿
			buttons.add(7);
		}
		if (buttonStatus.contains(4)) {
			//右-右下红
			buttons.add(8);
		}
		return buttons;
	}

	public static int hasBitN(List<Integer> bits, int idx) {
		return (bits != null && bits.contains(idx + 1)) ? 1 : 0;
	}

	public static BitSet parseBitsFromLong(long bits) {
		BitSet bitSet = new BitSet(64);
		int bitIndex = 0;
		while (bits != 0) {
			if ((bits & 1) == 1) {
				bitSet.set(bitIndex);
			}
			bits >>>= 1;
			bitIndex++;
		}
		return bitSet;
	}

	public static BitSet parseBitsFromInt(int bits) {
		BitSet bitSet = new BitSet(32);
		int bitIndex = 0;
		while (bits != 0) {
			if ((bits & 1) == 1) {
				bitSet.set(bitIndex);
			}
			bits >>>= 1;
			bitIndex++;
		}
		return bitSet;
	}

	/**
	 * 塔吊项目上报的List<Integer>里的值是1-based索引，需要将BitSet的0-based索引转换下
	 *
	 * @param bitSet bitSet
	 * @return list
	 */
	public static List<Integer> convertBitSetToList(BitSet bitSet) {
		List<Integer> bitList = new ArrayList<>();
		int i = bitSet.nextSetBit(0);
		while (i >= 0) {
			bitList.add(i + 1);
			i = bitSet.nextSetBit(i + 1);
		}
		return bitList;
	}

}
