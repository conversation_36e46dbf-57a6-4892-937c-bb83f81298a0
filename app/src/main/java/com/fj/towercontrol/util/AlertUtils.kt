package com.fj.towercontrol.util

import com.blankj.utilcode.util.CollectionUtils
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig
import com.fj.towercontrol.mqtt.entity.TowerData

/**
 * 预警判断工具类
 *
 * <AUTHOR>
 * @since 2025/8/20
 */
object AlertUtils {

	fun judge(towerData: TowerData): List<AlertInfo> {
		val configList = MemoryStore.getInstance().alarmConfigList
		if (CollectionUtils.isEmpty(configList)) {
			return emptyList()
		}

		val result = hashMapOf<Int, AlertInfo>()
		configList.forEach { config ->
			val alarmType = config.paramType
			if (result[alarmType]?.level == AlarmConfig.ALARM_LEVEL_SEVERE) {
				//如果某个告警类型已存在严重告警，则不再判断该类型的一般告警
				return@forEach
			}
			if (alarmType == AlarmConfig.ALARM_TYPE_INCLINATION) {
				if (config.compare(towerData.towerGradient)) {
					result[alarmType] = AlertInfo(alarmType, config.alarmLvl, towerData.towerGradient)
				}
			} else if (alarmType == AlarmConfig.ALARM_TYPE_LIGHTING) {
				MemoryStore.getInstance().lightningData?.electricFieldStrength?.toDouble()?.let {
					if (config.compare(it)) {
						result[alarmType] =
							AlertInfo(alarmType, config.alarmLvl, it)
					}
				}
			}
		}

		return result.values.toList()
	}

	fun AlarmConfig.compare(value: Double): Boolean {
		return when (comparatorType) {
			1 -> value > alarmValue
			2 -> value == alarmValue
			3 -> value < alarmValue
			4 -> value >= alarmValue
			5 -> value <= alarmValue
			else -> false
		}
	}
}

data class AlertInfo(
	/**
	 * 告警类型
	 */
	val type: Int,
	/**
	 * 告警级别
	 */
	val level: Int,
	/**
	 * 告警值
	 */
	val value: Double
)
