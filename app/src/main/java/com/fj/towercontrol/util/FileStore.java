package com.fj.towercontrol.util;

import android.text.TextUtils;

import com.blankj.utilcode.util.GsonUtils;
import com.fj.towercontrol.data.entity.NtripInfo;
import com.fj.towercontrol.data.entity.SlowdownDelayConfig;
import com.fj.towercontrol.data.entity.SystemConfig;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig;
import com.fjdynamics.app.logger.Logger;
import com.google.gson.reflect.TypeToken;
import com.tencent.mmkv.MMKV;

import java.lang.reflect.Type;
import java.util.List;

/**
 * 文件缓存
 *
 * <AUTHOR>
 */
public class FileStore {
	private static final String TAG = "FileStore";
	private static final MMKV mmkv = MMKV.defaultMMKV();
	private static final String KEY_NTRIP_CUSTOMIZE_INFO = "key_ntrip_customize_info";
	private static final String KEY_NTRIP_DISABLED = "key_ntrip_disabled";
	private static final String KEY_ECU_VERSION = "key_ecu_version";
	private static final String KEY_ALARM_CONFIG_LIST = "key_alarm_config_list";
	private static final String KEY_TOWER_CONFIG = "key_tower_config";
	private static final String KEY_SLOWDOWN_DELAY_CONFIG = "key_slowdown_config";
	private static final String KEY_LIFT_UP_HEIGHT = "KEY_LIFT_UP_HEIGHT";
	private static final String KEY_LIFT_DOWN_HEIGHT = "KEY_LIFT_DOWN_HEIGHT";
	/**
	 * 保存的系统配置
	 */
	private static final String KEY_SYSTEM_CONFIG = "KEY_SYSTEM_CONFIG";
	/**
	 * 平台系统配置 - 是否在333告警时限制控制
	 */
	private static final String KEY_LIMIT_CONTROL_BY_333 = "KEY_LIMIT_CONTROL_BY_333";
	/**
	 * 平台系统配置 - 安全帽距吊钩有效距离
	 */
	private static final String KEY_HELMET_VALID_RANGE = "KEY_HELMET_VALID_RANGE";

	/**
	 * 获取ntrip配置信息
	 *
	 * @return NtripInfo
	 */
	public static NtripInfo getNtripCustomize() {
		String infoStr = mmkv.decodeString(KEY_NTRIP_CUSTOMIZE_INFO, null);
		if (!TextUtils.isEmpty(infoStr)) {
			try {
				return GsonUtils.fromJson(infoStr, NtripInfo.class);
			} catch (Exception e) {
				Logger.e(TAG, "获取Ntrip信息异常");
			}
		}
		return null;
	}

	/**
	 * 保存ntrip配置信息
	 *
	 * @param ntripInfo ntripInfo
	 */
	public static void saveNtripCustomize(NtripInfo ntripInfo) {
		try {
			mmkv.encode(KEY_NTRIP_CUSTOMIZE_INFO, GsonUtils.toJson(ntripInfo));
		} catch (Exception e) {
			Logger.e(TAG, "saveNtripCustomize exception: " + e.getMessage());
		}
	}

	/**
	 * 获取ntrip是否被关闭
	 *
	 * @return ntrip是否被关闭
	 */
	public static boolean getNtripDisabled() {
		return mmkv.decodeBool(KEY_NTRIP_DISABLED, false);
	}

	/**
	 * 设置ntrip是否关闭
	 *
	 * @param disabled 是否关闭
	 */
	public static void setNtrpDisabled(boolean disabled) {
		mmkv.encode(KEY_NTRIP_DISABLED, disabled);
	}

	public static void setEcuVersion(String ecuVersion) {
		mmkv.encode(KEY_ECU_VERSION, ecuVersion);
	}

	public static String getEcuVersion() {
		return mmkv.decodeString(KEY_ECU_VERSION, null);
	}

	public static void setAlarmConfigList(List<AlarmConfig> alarmConfigList) {
		MemoryStore.getInstance().setAlarmConfigList(alarmConfigList);
		mmkv.encode(KEY_ALARM_CONFIG_LIST, GsonUtils.toJson(alarmConfigList));
	}

	public static List<AlarmConfig> getAlarmConfigList() {
		try {
			Type type = new TypeToken<List<AlarmConfig>>() {
			}.getType();
			return GsonUtils.fromJson(mmkv.decodeString(KEY_ALARM_CONFIG_LIST), type);
		} catch (Exception e) {
			Logger.e(TAG, "getAlarmConfigList exception: " + e.getMessage());
		}
		return null;
	}

	public static void setTowerConfig(TowerConfig towerConfig) {
		MemoryStore.getInstance().setTowerConfig(towerConfig);
		mmkv.encode(KEY_TOWER_CONFIG, GsonUtils.toJson(towerConfig));
	}

	public static TowerConfig getTowerConfig() {
		return GsonUtils.fromJson(mmkv.decodeString(KEY_TOWER_CONFIG), TowerConfig.class);
	}

	public static void setDelayConfig(SlowdownDelayConfig config) {
		String json = GsonUtils.toJson(config);
		mmkv.encode(KEY_SLOWDOWN_DELAY_CONFIG, json);
	}

	public static SlowdownDelayConfig getDelayConfig() {
		String json = mmkv.decodeString(KEY_SLOWDOWN_DELAY_CONFIG);
		return GsonUtils.fromJson(json, SlowdownDelayConfig.class);
	}

	public static double getLiftUpHeightConfig() {
		return mmkv.decodeDouble(KEY_LIFT_UP_HEIGHT, 15);
	}

	public static void setLiftUpHeightConfig(double height) {
		mmkv.encode(KEY_LIFT_UP_HEIGHT, height);
	}

	public static double getLiftDownHeightConfig() {
		return mmkv.decodeDouble(KEY_LIFT_DOWN_HEIGHT, 10);
	}

	public static void setLiftDownHeightConfig(double height) {
		mmkv.encode(KEY_LIFT_DOWN_HEIGHT, height);
	}

	public static void setSystemConfig(SystemConfig config) {
		MemoryStore.getInstance().setSystemConfig(config);
		mmkv.encode(KEY_SYSTEM_CONFIG, GsonUtils.toJson(config));
	}

	public static SystemConfig getSystemConfig() {
		String json = mmkv.decodeString(KEY_SYSTEM_CONFIG);
		if (TextUtils.isEmpty(json)) return new SystemConfig();
		try {
			return GsonUtils.fromJson(json, SystemConfig.class);
		} catch (Exception e) {
			Logger.e(TAG, "getSystemConfig exception: " + e.getMessage());
		}
		return new SystemConfig();
	}

	public static void setLimitControlBy333(boolean limit) {
		mmkv.encode(KEY_LIMIT_CONTROL_BY_333, limit);
		MemoryStore.getInstance().setLimitControlBy333(limit);
	}

	public static boolean getLimitControlBy333() {
		return mmkv.decodeBool(KEY_LIMIT_CONTROL_BY_333, false);
	}
}
