package com.fj.towercontrol.util;

import com.fj.fjprotocol.data.GeoData;
import com.fj.towercontrol.data.entity.EcuLog;
import com.fj.towercontrol.data.entity.EcuRealtimeData;
import com.fj.towercontrol.data.entity.TowerBaseConfig;
import com.fj.towercontrol.data.entity.TowerConfig;

/**
 * 计算工具类
 *
 * <AUTHOR>
 */
public class Calculator {
	private static final String TAG = "Calculator";
	/**
	 * 地球半径，单位为千米
	 */
	private static final int R = 6371;

	/**
	 * 计算吊钩下降高度
	 *
	 * @param armDegree      吊臂与水平面倾角
	 * @param armLength      吊臂长度
	 * @param towerTopHeight 塔顶高程
	 * @param antennaHeight  塔顶校准值高度
	 * @param hookHeight     吊钩高程
	 * @return 吊钩下降高度
	 */
	public static double calculateDescentHeight(double armDegree,
																							double armLength,
																							double towerTopHeight,
																							double antennaHeight,
																							double hookHeight) {
		return towerTopHeight + antennaHeight - hookHeight + Math.sin(Math.toRadians(armDegree)) * armLength;
	}

	/**
	 * 计算雷达旋转角度
	 *
	 * @param towerHeading 塔身航向角
	 * @param hookHeading  吊钩航向角
	 * @return 雷达旋转角度
	 */
	public static double calculateRadarRotation(double towerHeading, double hookHeading) {
		double towerAngle = convertTo360(convertTo360(towerHeading) - 155);
		double hookAngle = convertTo360(convertTo360(hookHeading) - 90);
		return convertTo360(hookAngle - towerAngle);
	}

	/**
	 * 将[-180,180]的角度转换为[0,360]的角度
	 *
	 * @param input 航向
	 * @return 角度
	 */
	private static double convertTo360(double input) {
		return (input + 360) % 360;
	}

	/**
	 * 雷达距离转换成吊物距障碍物水平距离
	 *
	 * @param radarDistance 雷达距离
	 * @param radarAngle    雷达和检测目标的角度
	 * @param cargoRadius   吊物半径
	 * @param installAngle  雷达安装偏移角度
	 * @return 吊物距障碍物垂直距离
	 */
	public static double calculateTargetHorizontalDistance(double radarDistance,
																												 double radarAngle,
																												 double cargoRadius,
																												 double installAngle) {
		double shiftAngle;
		if (installAngle < 0) {
			//俯视安装
			if (radarAngle <= 0) {
				shiftAngle = Math.abs(radarAngle - installAngle);
			} else if (radarAngle >= installAngle) {
				shiftAngle = radarAngle - installAngle;
			} else {
				shiftAngle = installAngle - radarAngle;
			}
		} else {
			//仰视安装
			if (radarAngle >= 0) {
				shiftAngle = radarAngle + installAngle;
			} else if (radarAngle <= (installAngle * -1)) {
				shiftAngle = Math.abs(radarAngle) - Math.abs(installAngle);
			} else {
				shiftAngle = Math.abs(installAngle) - Math.abs(radarAngle);
			}
		}
		return Math.cos(Math.toRadians(shiftAngle)) * radarDistance + 0.325 - cargoRadius;
	}

	/**
	 * 雷达距离转换成吊物距障碍物垂直距离
	 *
	 * @param radarDistance 雷达距离
	 * @param radarAngle    雷达和检测目标的角度
	 * @param installAngle  雷达安装偏移角度
	 * @return 障碍物垂直距离
	 */
	public static double calculateTargetVerticalDistance(double radarDistance,
																											 double radarAngle,
																											 double installAngle) {
		double shiftAngle;
		if (radarAngle <= 0) {
			shiftAngle = Math.abs(radarAngle - installAngle);
		} else {
			shiftAngle = installAngle - radarAngle;
		}
		return Math.sin(Math.toRadians(shiftAngle)) * radarDistance;
	}

	/**
	 * 计算两点水平距离
	 *
	 * @param lat1 lat1
	 * @param lon1 lon1
	 * @param lat2 lat2
	 * @param lon2 lon2
	 * @return 距离(m)
	 */
	public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
		//将角度转换为弧度
		double lat1Rad = Math.toRadians(lat1);
		double lat2Rad = Math.toRadians(lat2);

		//计算经纬度差的一半
		double deltaLat = Math.toRadians(lat2 - lat1) / 2;
		double deltaLon = Math.toRadians(lon2 - lon1) / 2;

		//使用Haversine公式计算距离
		double a = Math.pow(Math.sin(deltaLat), 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.pow(Math.sin(deltaLon), 2);
		double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

		return R * c * 1000;
	}

	/**
	 * 计算两点三维空间距离
	 *
	 * @return m
	 */
	public static double calculate3DDistance(double lat1, double lon1, double alt1, double lat2, double lon2, double alt2) {
		//计算地球表面上的距离
		double surfaceDistance = calculateDistance(lat1, lon1, lat2, lon2);
		//计算高度差
		double altitudeDifference = alt1 - alt2;
		//使用勾股定理计算空间距离
		return Math.sqrt(Math.pow(surfaceDistance, 2) + Math.pow(altitudeDifference, 2));
	}

	/**
	 * 计算摄像头高程和离地高度
	 *
	 * @param ecuData     ecu实时状态
	 * @param ecuLog      ecu日志
	 * @param towerConfig 塔吊配置
	 * @return [摄像头高程, 摄像头离地高度]
	 */
	public static Double[] calcCameraHeight(EcuRealtimeData ecuData, EcuLog ecuLog, TowerConfig towerConfig) {
		Double[] result = new Double[2];
		if (ecuData == null
			|| towerConfig == null
			|| towerConfig.getTowerBaseConfig() == null) {
			return result;
		}

		TowerBaseConfig towerBaseConfig = towerConfig.getTowerBaseConfig();
		Double jibLength = towerBaseConfig.getTowerArmLength();
		if (jibLength == null) {
			return result;
		}

		GeoData towerTopPos = towerBaseConfig.getTowerTopPos();
		Double towerTopHeight = null;
		if (ecuLog != null
			&& GeoUtils.isGpsValid(ecuLog.getTowerGpsStatus())) {
			towerTopHeight = ecuLog.getTowerLocation().getAlt() + MemoryStore.getInstance().getHookGeoidalHeight();
		} else if (towerTopPos != null) {
			towerTopHeight = towerTopPos.getAlt();
		}
		if (towerTopHeight == null) {
			return result;
		}
		//大臂前端距塔顶高度
		double diffHeight = jibLength * Math.sin(Math.toRadians(ecuData.getLuffingAngle()));

		//摄像头高程
		result[0] = towerTopHeight + diffHeight;
		GeoData towerBasePos = towerBaseConfig.getTowerPos();
		if (towerBasePos != null) {
			//摄像头离地高度
			result[1] = result[0] - towerBasePos.getAlt();
		}
		return result;
	}
}
