package com.fj.towercontrol.mqtt.entity

import com.blankj.utilcode.util.CollectionUtils
import com.fj.fjprotocol.data.GeoData
import com.fj.towercontrol.consts.CraneType
import com.fj.towercontrol.data.entity.BatteryInfo
import com.fj.towercontrol.data.entity.CargoWeightInfo
import com.fj.towercontrol.data.entity.EcuLog
import com.fj.towercontrol.data.entity.EcuRealtimeData
import com.fj.towercontrol.data.entity.ImuCalibration
import com.fj.towercontrol.data.entity.Position
import com.fj.towercontrol.data.entity.TowerConfig
import com.fj.towercontrol.data.entity.TowerCraneData
import com.fj.towercontrol.data.entity.WeatherStationData
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig
import com.fj.towercontrol.util.AlertUtils
import com.fj.towercontrol.util.Calculator
import com.fj.towercontrol.util.EcuDataParser
import com.fj.towercontrol.util.EulerAngle2QuatUtil
import com.fj.towercontrol.util.GeoUtils
import com.fj.towercontrol.util.MemoryStore
import com.fj.towercontrol.util.toPosition
import com.google.gson.annotations.SerializedName
import kotlin.math.round

/**
 * 塔吊数据类
 *
 * <AUTHOR>
 */

data class TowerData(
	var type: String = "metric",
	/**
	 * utc时间戳
	 */
	var timestamp: Long = 0L,
	/**
	 * 工作状态 1：待机 2：空闲 3：工作 4：锁定
	 */
	var workStatus: String? = null,
	/**
	 * 当前工作模式 1：驾舱手动控制模式 2：远程手动控制模式 3：远程自动控制模式
	 */
	var workMode: String? = null,
	/**
	 * 风向
	 */
	var windDirection: Double = 0.0,
	/**
	 * 风速
	 */
	var windSpeed: Double = 0.0,
	/**
	 * 温度
	 */
	var temperature: Double = 0.0,
	/**
	 * 湿度
	 */
	var humidity: Double = 0.0,
	/**
	 * 吊钩三维地理数据
	 */
	@SerializedName("cliver")
	var hangingHook: Position? = null,
	/**
	 * 塔顶三维地理数据
	 */
	var towerTop: Position? = null,
	/**
	 * 塔身倾斜度
	 */
	var towerGradient: Double = 0.0,
	/**
	 * 2分钟平均风速
	 */
	@SerializedName("2minWindSpeed")
	var averageWindSpeed: Double = 0.0,
	/**
	 * 吊钩距地面的高度
	 */
	var heightAboveGround: Double = 0.0,
	/**
	 * 当前限重(kg)
	 */
	var weightLimit: Double = 0.0,
	/**
	 * 当前负载下半径限制
	 */
	var radiusLimit: Double = 0.0,
	/**
	 * 工作半径
	 */
	var workRadius: Double? = null,
	/**
	 * 塔身高度
	 */
	var towerHeight: Double = 0.0,
	/**
	 * 吊钩下降高度
	 */
	var descentHeight: Double? = null,
	/**
	 * 遥控-吊钩挡位,-4~0~4,负值下降
	 */
	@SerializedName("cliverGear")
	var remoteHookGear: Int = 0,
	/**
	 * 遥控-小车挡位,-4~0~4,负值后退
	 */
	@SerializedName("trolleyGear")
	var remoteTrolleyGear: Int = 0,
	/**
	 * 遥控-回转挡位,-2~0~2,负值向左
	 */
	@SerializedName("gyrationGear")
	var remoteSpinGear: Int = 0,
	/**
	 * 前臂仰角
	 */
	@SerializedName("forearmElevation")
	var armDegree: Double = 0.0,
	/**
	 * ecu上报的遥控-使能按钮状态
	 */
	var remoteStatus: MutableList<Int>? = null,
	/**
	 * 电池剩余电量百分比
	 */
	var batterySoc: Double? = null,
	/**
	 * 电池状态：0静止，1充电，2放电
	 */
	var batteryStatus: Int? = null,
	/**
	 * 吊物重量
	 */
	var liftWeight: Int? = null,
	/**
	 * 智能吊钩读取到的吊物重量
	 */
	var craneLiftWeight: Int? = null,
	/**
	 * ecu上报的工作半径
	 */
	var craneWorkRadius: Double = 0.0,
	/**
	 * 计算出来的吊钩下降高度
	 */
	var craneDescentHeight: Double = 0.0,
	/**
	 * 倾角方向
	 */
	var inclinationDirection: Double = 0.0,
	/**
	 * 放迫力灯信号<br>
	 * 0:灯灭<br>
	 * 1:灯亮
	 */
	var lightSignal: Int = 0,
	/**
	 * 雷达旋转角度
	 */
	var radarShiftingAngle: Double = 0.0,
	/**
	 * 塔吊是否启动
	 */
	var powerOn: Int = 0,
	/**
	 * 故障码
	 */
	var errorCode: MutableList<Int>? = null,
	/**
	 * 起升变频器故障代码
	 */
	var liftError: String? = null,
	/**
	 * 回转变频器故障代码
	 */
	var slewError: String? = null,
	/**
	 * 变幅变频器故障代码
	 */
	var amplitudeError: String? = null,
	/**
	 * 急停信号
	 */
	var emergencyStop: Int = 0,
	/**
	 * 大臂航向
	 */
	var towerYaw: Double = 0.0,
	/**
	 * 登录用户uid
	 */
	var uid: Long? = null,
	/**
	 * 吊钩摄像头高程
	 */
	var cameraHeight: Double = 0.0,
	/**
	 * 吊钩摄像头离地高度
	 */
	var cameraHeightAboveGround: Double = 0.0,
	/**
	 * 当前是是否限控
	 */
	var limitControl: Boolean = false,
	/**
	 * 当前是否是远控
	 */
	var remote: Boolean = false,
	/**
	 * 当前蜗速状态
	 */
	var wormSpeed: Boolean = false,
) {
	companion object {

		@JvmStatic
		fun build(
			ecuData: EcuRealtimeData,
			ecuLog: EcuLog?,
			cargoWeightInfo: CargoWeightInfo?
		): TowerData {
			val towerData = TowerData()
			towerData.timestamp = System.currentTimeMillis()
			towerData.uid = MemoryStore.getInstance().uid
			towerData.workStatus = ecuData.craneStatus.toString()
			if (ecuData.modeChild == 0xa0) {
				towerData.workMode = ecuData.modeChild.toString()
			} else {
				towerData.workMode = ecuData.modeParent.toString()
			}
			towerData.emergencyStop = ecuData.emergencyStop
			towerData.windDirection = ecuData.windDirection
			towerData.windSpeed = ecuData.windSpeed
			towerData.temperature = ecuData.temperature
			towerData.humidity = ecuData.humidity
			if (ecuLog != null) {
				val hookPos: GeoData = ecuLog.hookLocation
				if (GeoUtils.isGpsValid(ecuLog.hookGpsStatus)) {
					//安全帽里的高程使用的是大地高度，得把吊钩的海拔高度转换成实际大地高(海拔高+似大地水准面差距-天线相位中心高)
					val altAfterCalibration =
						hookPos.alt + MemoryStore.getInstance().hookGeoidalHeight - 0.1221
					hookPos.alt = altAfterCalibration
					towerData.hangingHook = hookPos.toPosition()
				}
				val towerTopPos: GeoData = ecuLog.towerLocation
				if (GeoUtils.isGpsValid(ecuLog.towerGpsStatus)) {
					towerData.towerTop = towerTopPos.toPosition()
				}
			}

			towerData.averageWindSpeed = ecuData.averageWindSpeed
			towerData.heightAboveGround = ecuData.hookHeightAboveGround
			towerData.errorCode = EcuDataParser.convertBitSetToList(ecuData.errorLow)
			towerData.weightLimit = ecuData.loadingLimit * 1000.0
			towerData.radiusLimit = ecuData.radiusLimit
			towerData.towerHeight = ecuData.towerCranHeight
			towerData.remoteTrolleyGear = ecuData.trolleyGear
			towerData.remoteSpinGear = ecuData.slewGear
			towerData.remoteHookGear = ecuData.hookGear
			towerData.armDegree = ecuData.luffingAngle
			towerData.remoteStatus = EcuDataParser.convertBitSetToList(ecuData.remoteFunction)
			towerData.radarShiftingAngle = Calculator.calculateRadarRotation(ecuData.yaw, ecuData.hookYaw)

			val towerConfig: TowerConfig? = MemoryStore.getInstance().towerConfig

			//计算塔身倾斜度和倾斜方向
			var imuCalibration: ImuCalibration? = null
			if (towerConfig != null && towerConfig.towerBaseConfig != null) {
				imuCalibration = towerConfig.towerBaseConfig.imuCalibration
			}
			val imu = EulerAngle2QuatUtil.calcVecCog(
				ecuData.pitch,
				ecuData.roll + (imuCalibration?.rollAngle ?: 0.0)
			)
			var towerGradient = imu[0]
			if (towerGradient.isNaN()) {
				towerGradient = 0.0
			}
			towerData.towerGradient = towerGradient
			var inclinationDirection = imu[1]
			if (inclinationDirection.isNaN()) {
				inclinationDirection = 0.0
			}
			towerData.inclinationDirection = inclinationDirection
			towerData.lightSignal = ecuData.lightSignal
			towerData.powerOn = ecuData.powerOn

			var armLength = 0.0
			if (towerConfig != null && towerConfig.towerBaseConfig != null) {
				armLength = towerConfig.towerBaseConfig.towerArmLength ?: 0.0
			}
			val calculatedDescentHeight = Calculator.calculateDescentHeight(
				ecuData.luffingAngle,
				armLength,
				ecuLog?.towerLocation?.alt ?: 0.0,
				0.0,
				ecuLog?.hookLocation?.alt ?: 0.0
			)
			towerData.craneDescentHeight = round(calculatedDescentHeight * 100.0) / 100.0
			towerData.craneWorkRadius = ecuData.workRadius
			towerData.craneLiftWeight = cargoWeightInfo?.netWeight

			//离地高度取值优先级: 永茂塔机 > 三一塔机
			var maxHeightAboveGround = 76.4
			if (towerConfig != null &&
				towerConfig.towerBaseConfig != null &&
				towerConfig.towerBaseConfig.hookConfig != null
			) {
				maxHeightAboveGround = towerConfig.towerBaseConfig.hookConfig.maxHeightAboveGround
			}
			towerData.descentHeight = maxHeightAboveGround - ecuData.sanyHookHeightAboveGround
			towerData.workRadius = ecuData.sanyWorkRadius
			towerData.heightAboveGround = ecuData.sanyHookHeightAboveGround
			//平台配置皮重
			var peeledWeight = 0
			if (towerConfig != null && towerConfig.calibration != null) {
				peeledWeight = towerConfig.calibration.peeledWeight.toInt()
			}
			//去皮后重量
			val liftWeight = (ecuData.sanyLiftWeight - peeledWeight).toInt()
			if (MemoryStore.getInstance().systemConfig.craneType == CraneType.XCMG.ordinal) {
				towerData.liftWeight = cargoWeightInfo?.netWeight
			} else {
				towerData.liftWeight = liftWeight
			}

			//如果app能读到气象站数据，使用app读取到的数据
			val weatherData: WeatherStationData? = MemoryStore.getInstance().weatherStationData
			if (weatherData != null) {
				towerData.temperature = weatherData.temperature
				towerData.humidity = weatherData.humidity
				towerData.windDirection = weatherData.windDirection.toDouble()
				towerData.windSpeed = weatherData.windSpeed * 3.6
				towerData.averageWindSpeed = weatherData.windSpeedAverage * 3.6
			}

			//cic塔机数据
			val towerCraneData: TowerCraneData? = MemoryStore.getInstance().towerCraneData
			if (towerCraneData != null) {
				towerData.workRadius = towerCraneData.workRadius
				towerData.descentHeight = towerCraneData.hookHeight
				towerData.liftWeight = (towerCraneData.cargoWeight - peeledWeight).coerceAtLeast(0)
			}

			//吊钩电池数据
			val batteryInfo: BatteryInfo? = MemoryStore.getInstance().batteryInfo
			if (batteryInfo != null) {
				towerData.batterySoc = batteryInfo.percent
				towerData.batteryStatus = batteryInfo.status
			}

			//变频器故障代码
			towerData.liftError = convertInverterErrorCode(ecuData.liftError)
			towerData.slewError = convertInverterErrorCode(ecuData.rotateError)
			towerData.amplitudeError = convertInverterErrorCode(ecuData.amplitudeError)

			//大臂航向
			val towerYaw = ((ecuData.yaw + 360.0) % 360.0 + 180.0) % 360.0
			towerData.towerYaw = if (towerYaw > 180.0) towerYaw - 360.0 else towerYaw

			//计算吊钩摄像头高程与离地高度
			val cameraHeights = Calculator.calcCameraHeight(ecuData, ecuLog, towerConfig)
			if (cameraHeights.size >= 2) {
				cameraHeights[0]?.let { towerData.cameraHeight = it }
				cameraHeights[1]?.let { towerData.cameraHeightAboveGround = it }
			}

			towerData.limitControl = ecuData.limitControl > 0
			towerData.remote = ecuData.workMode != 0
			towerData.wormSpeed = ecuData.wormSpeed != 0

			AlertUtils.judge(towerData).forEach { alarmInfo ->
				if (alarmInfo.type == AlarmConfig.ALARM_TYPE_INCLINATION) {
					//塔身倾斜度预警上报
					if (alarmInfo.level == AlarmConfig.ALARM_LEVEL_NORMAL) {
						towerData.errorCode?.add(41)
					} else if (alarmInfo.level == AlarmConfig.ALARM_LEVEL_SEVERE) {
						towerData.errorCode?.add(42)
					}
				} else if (alarmInfo.type == AlarmConfig.ALARM_TYPE_LIGHTING) {
					//雷电预警上报
					if (alarmInfo.level == AlarmConfig.ALARM_LEVEL_NORMAL) {
						towerData.errorCode?.add(43)
					} else if (alarmInfo.level == AlarmConfig.ALARM_LEVEL_SEVERE) {
						towerData.errorCode?.add(44)
					}
				}
			}

			if (CollectionUtils.isEmpty(towerData.errorCode)) {
				//如果没有错误码，放一个空的0表示正常
				towerData.errorCode!!.add(0)
			}
			return towerData
		}

		private fun convertInverterErrorCode(rawErrorCode: Int): String? {
			if (rawErrorCode == 0) {
				return null
			}
			return "E$rawErrorCode"
		}
	}
}

