package com.fj.towercontrol.widget;

//import android.app.Dialog;
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.WindowManager;
//import android.widget.EditText;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
//import androidx.fragment.app.FragmentManager;
//
//import com.blankj.utilcode.util.ToastUtils;
//import com.fj.towercontrol.R;
//import com.fj.towercontrol.util.FileStore;
//import com.fjdynamics.app.logger.Logger;

/**
 * 底部雷达配置弹窗
 *
 * <AUTHOR>
 */
public class RadarConfigDialog extends DialogFragment {
//
//	public static String TAG = "RadarConfigDialog";
//
//	@NonNull
//	@Override
//	public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
//		View view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_radar_config, null);
//		EditText etDistance = view.findViewById(R.id.et_distance);
//		EditText etStart = view.findViewById(R.id.et_start);
//		EditText etEnd = view.findViewById(R.id.et_end);
//		EditText etInstallAngle = view.findViewById(R.id.et_install_angle);
//		EditText etGroundThreshold = view.findViewById(R.id.et_ground_distance);
//		EditText etHorizontal = view.findViewById(R.id.et_lift_horizontal_threshold);
//		EditText etVertical = view.findViewById(R.id.et_lift_vertical_threshold);
//		EditText etRadarToLiftDistance = view.findViewById(R.id.et_radar_to_lift_distance);
//		etDistance.setText(String.valueOf(FileStore.getRadarDistanceLimit()));
//		etStart.setText(String.valueOf(FileStore.getRadarStartAngle()));
//		etEnd.setText(String.valueOf(FileStore.getRadarEndAngle()));
//		etInstallAngle.setText(String.valueOf(FileStore.getRadarInstallAngle()));
//		etGroundThreshold.setText(String.valueOf(FileStore.getGroundHeightThreshold()));
//		etHorizontal.setText(String.valueOf(FileStore.getLiftHorizontalThreshold()));
//		etVertical.setText(String.valueOf(FileStore.getLiftVerticalThreshold()));
//		etRadarToLiftDistance.setText(String.valueOf(FileStore.getRadarToLiftDistance()));
//		AlertDialog dialog = new AlertDialog.Builder(requireContext())
//			.setTitle("雷达配置")
//			.setView(view)
//			.setNegativeButton("取消", null)
//			.setPositiveButton("确定", (dialogInterface, which) -> {
//				try {
//					String distanceText = etDistance.getText().toString().trim();
//					String startAngleText = etStart.getText().toString().trim();
//					String endAngleText = etEnd.getText().toString().trim();
//					String installAngleText = etInstallAngle.getText().toString().trim();
//					String groundThresholdText = etGroundThreshold.getText().toString().trim();
//					String horizontalThresholdText = etHorizontal.getText().toString().trim();
//					String verticalThresholdText = etVertical.getText().toString().trim();
//					String radarToLiftDistanceText = etRadarToLiftDistance.getText().toString().trim();
//					if (TextUtils.isEmpty(distanceText)
//						|| TextUtils.isEmpty(startAngleText)
//						|| TextUtils.isEmpty(endAngleText)
//						|| TextUtils.isEmpty(installAngleText)
//						|| TextUtils.isEmpty(groundThresholdText)
//						|| TextUtils.isEmpty(horizontalThresholdText)
//						|| TextUtils.isEmpty(verticalThresholdText)
//						|| TextUtils.isEmpty(radarToLiftDistanceText)) {
//						ToastUtils.showShort("配置不能为空！");
//						return;
//					}
//
//					double distance = Double.parseDouble(distanceText);
//					double startAngle = Double.parseDouble(startAngleText);
//					double endAngle = Double.parseDouble(endAngleText);
//					double installAngle = Double.parseDouble(installAngleText);
//					double groundThreshold = Double.parseDouble(groundThresholdText);
//					double horizontalThreshold = Double.parseDouble(horizontalThresholdText);
//					double verticalThreshold = Double.parseDouble(verticalThresholdText);
//					double radarToLiftDistance = Double.parseDouble(radarToLiftDistanceText);
//					if (distance < 0
//						|| startAngle < 0
//						|| endAngle < startAngle
//						|| groundThreshold < 0
//						|| horizontalThreshold < 0
//						|| verticalThreshold < 0
//						|| radarToLiftDistance < 0) {
//						ToastUtils.showShort("输入异常");
//						return;
//					}
//					FileStore.setRadarDistanceLimit(distance);
//					FileStore.setRadarStartAngle(startAngle);
//					FileStore.setRadarEndAngle(endAngle);
//					FileStore.setRadarInstallAngle(installAngle);
//					FileStore.setGroundHeightThreshold(groundThreshold);
//					FileStore.setLiftHorizontalThreshold(horizontalThreshold);
//					FileStore.setLiftVerticalThreshold(verticalThreshold);
//					FileStore.setRadarToLiftDistance(radarToLiftDistance);
//
//					dialogInterface.dismiss();
//				} catch (Exception e) {
//					Logger.e(TAG, "config exception: " + e.getMessage());
//					dialogInterface.dismiss();
//				}
//			})
//			.create();
//
//		// 设置隐藏导航栏
//		dialog.getWindow()
//			.setFlags(
//				WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
//				WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
//		dialog.show();
//		dialog.getWindow()
//			.getDecorView()
//			.setSystemUiVisibility(
//				View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//					| View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//					| View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//					| View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
//					| View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//					| View.SYSTEM_UI_FLAG_FULLSCREEN);
//		dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
//
//		return dialog;
//	}
//
//	@Override
//	public void show(@NonNull FragmentManager manager, @Nullable String tag) {
//		setCancelable(false);
//		super.show(manager, tag);
//	}
}
