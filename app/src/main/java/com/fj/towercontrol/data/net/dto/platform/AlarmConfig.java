package com.fj.towercontrol.data.net.dto.platform;

import androidx.annotation.NonNull;

/**
 * 平台配置的告警规则
 *
 * <AUTHOR>
 */
public class AlarmConfig {
	/**
	 * 告警类型 - 障碍物距离
	 */
	public static final int ALARM_TYPE_OBSTACLE_DISTANCE = 1;
	/**
	 * 告警类型 - 载荷
	 */
	public static final int ALARM_TYPE_WEIGHT = 2;
	/**
	 * 告警类型 - 风速
	 */
	public static final int ALARM_TYPE_WIND_SPEED = 3;
	/**
	 * 告警类型 - 围栏距离
	 */
	public static final int ALARM_TYPE_FENCE_DISTANCE = 4;
	/**
	 * 告警类型 - 塔身倾斜度
	 */
	public static final int ALARM_TYPE_INCLINATION = 5;
	/**
	 * 告警类型 - 雷电预警
	 */
	public static final int ALARM_TYPE_LIGHTING = 6;
	/**
	 * 一般告警级别
	 */
	public static final int ALARM_LEVEL_NORMAL = 3;
	/**
	 * 严重告警级别
	 */
	public static final int ALARM_LEVEL_SEVERE = 7;
	/**
	 * 告警id
	 */
	private long id;
	/**
	 * 告警编号
	 */
	private String alarmNo;
	/**
	 * 告警名称
	 */
	private String alarmName;
	/**
	 * 告警级别：3-一般，7-严重
	 */
	private int alarmLvl;
	/**
	 * 状态：0-关闭，1-开启
	 */
	private int status;
	/**
	 * 参数名： 1-障碍物距离（单位m） 2-载荷（单位kg） 3-风速（单位km/h） 4-围栏距离（单位m）
	 */
	private int paramType;
	/**
	 * 比较符： 1-> 2-= 3-< 4->= 5-=<
	 */
	private int comparatorType;
	/**
	 * 告警值
	 */
	private double alarmValue;
	/**
	 * 机构id
	 */
	private long orgId;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getAlarmNo() {
		return alarmNo;
	}

	public void setAlarmNo(String alarmNo) {
		this.alarmNo = alarmNo;
	}

	public String getAlarmName() {
		return alarmName;
	}

	public void setAlarmName(String alarmName) {
		this.alarmName = alarmName;
	}

	public int getAlarmLvl() {
		return alarmLvl;
	}

	public void setAlarmLvl(int alarmLvl) {
		this.alarmLvl = alarmLvl;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getParamType() {
		return paramType;
	}

	public void setParamType(int paramType) {
		this.paramType = paramType;
	}

	public int getComparatorType() {
		return comparatorType;
	}

	public void setComparatorType(int comparatorType) {
		this.comparatorType = comparatorType;
	}

	public double getAlarmValue() {
		return alarmValue;
	}

	public void setAlarmValue(double alarmValue) {
		this.alarmValue = alarmValue;
	}

	public long getOrgId() {
		return orgId;
	}

	public void setOrgId(long orgId) {
		this.orgId = orgId;
	}

	@NonNull
	@Override
	public String toString() {
		return "AlarmConfig{"
			+ "id="
			+ id
			+ ", alarmNo='"
			+ alarmNo
			+ '\''
			+ ", alarmName='"
			+ alarmName
			+ '\''
			+ ", alarmLvl="
			+ alarmLvl
			+ ", status="
			+ status
			+ ", paramType="
			+ paramType
			+ ", comparatorType="
			+ comparatorType
			+ ", alarmValue="
			+ alarmValue
			+ ", orgId="
			+ orgId
			+ '}';
	}
}
