package com.fj.towercontrol.data.entity

import android.util.Log
import com.blankj.utilcode.util.GsonUtils
import com.fj.towercontrol.BuildConfig
import com.fj.towercontrol.util.EcuDataParser
import com.fjdynamics.app.logger.Logger
import okio.Buffer
import java.util.BitSet

/**
 * ecu实时状态上报数据
 *
 * <AUTHOR>
 */
data class EcuRealtimeData(
	/**
	 * ECU系统时间戳
	 */
	var timestamp: Long = 0,
	/**
	 * 当前模式（父模式）
	 * <p>
	 * 0x00: 无模式<br>
	 * 0x01: 人工(驾舱手动)<br>
	 * 0x02: 遥控(远程手动)<br>
	 * 0x03: 半自动(远程自动)<br>
	 */
	var modeParent: Int = 0,
	/**
	 * 当前模式（子模式）
	 * <p>
	 * 0x00:手动模式<br>
	 * 0x01:遥控模式<br>
	 * 0x02:半自动模式：移动至A点<br>
	 * 0x03:半自动模式：移动至B点<br>
	 * 0x05:空闲模式<br>
	 * 0x10:移动到了A点<br>
	 * 0x11:移动到了B点<br>
	 * <p>
	 * 以下为异常模式，在半自动作业过程中出现异常时触发，在没接收到新的模式指令前保持当前异常模式
	 * <p>
	 * 0xA0:异常模式<br>
	 * 0xA1:非固定解超时<br>
	 * 0xA2:锁定模式<br>
	 */
	var modeChild: Int = 0,
	/**
	 * 吊塔当前状态
	 * <p>
	 * 0x01: 待机<br>
	 * 0x02: 空闲<br>
	 * 0x03: 工作中<br>
	 * 0x04: 锁定<br>
	 * 0x05: 手动
	 */
	var craneStatus: Int = 0,
	/**
	 * 错误码高位
	 */
	var errorHigh: BitSet = BitSet(),
	/**
	 * 错误码低位
	 */
	var errorLow: BitSet = BitSet(),
	/**
	 * 地面驾舱指令
	 */
	var instruction: BitSet = BitSet(),
	/**
	 * 遥控-使能按钮
	 *
	 * bit0:开机<br>
	 * bit1:急停信号<br>
	 * bit2:回转刹车<br>
	 * bit3:喇叭<br>
	 * bit4:遥控模式<br>
	 * bit5:自动模式<br>
	 * bit6:左手柄使能开关<br>
	 * bit7:右手柄使能开关
	 */
	var remoteFunction: BitSet = BitSet(),
	/**
	 * 遥控-吊钩挡位,-4~0~4,负值下降
	 */
	var hookGear: Int = 0,
	/**
	 * 遥控-小车挡位,-4~0~4,负值后退
	 */
	var trolleyGear: Int = 0,
	/**
	 * 遥控-回转挡位,-2~0~2,负值向左
	 */
	var slewGear: Int = 0,
	/**
	 * 吊塔-自身状态（高位）
	 * <p>
	 * bit0: manual_lock:1;<br>
	 * bit1-7: reserved:7;
	 */
	var craneSelfStatusHigh: BitSet = BitSet(),
	/**
	 * 吊塔-自身状态（低位）
	 * <p>
	 * reserve1:1;<br>
	 * on:1;<br>
	 * is_sm0_closed:1;<br>
	 * is_sm1_closed:1;<br>
	 * is_rm0_closed:1;<br>
	 * reserve2:1;<br>
	 * reserve3:1;<br>
	 * reserve4:1;<br>
	 */
	var craneSelfStatusLow: BitSet = BitSet(),
	/**
	 * 塔吊当前错误状态
	 */
	var towerCraneErrorStatus: Int = 0,
	/**
	 * 回转制动迫力灯,1开，0关
	 */
	var lightSignal: Int = 0,
	/**
	 * 风向
	 */
	var windDirection: Double = 0.0,
	/**
	 * 风速
	 */
	var windSpeed: Double = 0.0,
	/**
	 * 温度
	 */
	var temperature: Double = 0.0,
	/**
	 * 湿度
	 */
	var humidity: Double = 0.0,
	/**
	 * 2分钟平均风速
	 */
	var averageWindSpeed: Double = 0.0,
	/**
	 * 吊钩绞盘振动
	 */
	val hookHoistVibration: Vibration = Vibration(),
	/**
	 * 小车绞盘振动
	 */
	val trolleyHoistVibration: Vibration = Vibration(),
	/**
	 * 吊钩到中轴距离（吊钩半径）
	 */
	var workRadius: Double = 0.0,
	/**
	 * 塔身倾斜度
	 */
	var towerInclination: Double = 0.0,
	/**
	 * 吊钩距地面高度
	 */
	var hookHeightAboveGround: Double = 0.0,
	/**
	 * 塔吊高度（塔顶高程 - 塔基高程）
	 */
	var towerCranHeight: Double = 0.0,
	/**
	 * 当前半径下负载限制(t)
	 */
	var loadingLimit: Double = 0.0,
	/**
	 * 当前负载下半径限制(m)
	 */
	var radiusLimit: Double = 0.0,
	/**
	 * 塔吊类型: 0-永茂;1-三一;
	 */
	var towerCraneType: Int = 0,
	/**
	 * 动臂俯仰角
	 */
	var luffingAngle: Double = 0.0,
	/**
	 * 惯导天线滚转角,单位：deg, [-180,180]
	 */
	var roll: Double = 0.0,
	/**
	 * 惯导天线俯仰角,单位：deg，[-90,90]
	 */
	var pitch: Double = 0.0,
	/**
	 * 塔身双天线航向,单位：deg，[-180,180]
	 */
	var yaw: Double = 0.0,
	/**
	 * 吊钩双天线航向,单位：deg，[-180,180]
	 */
	var hookYaw: Double = 0.0,
	/**
	 * 吊钩距吊臂所在锤面的距离
	 */
	var hook1: Double = 0.0,
	/**
	 * 吊钩在吊臂所在锤面的投影距旋转轴的距离
	 */
	var hook2: Double = 0.0,
	/**
	 * 吊钩吊绳长度
	 */
	var ropeLength: Double = 0.0,
	/**
	 * ECU->塔机 指令
	 */
	var ecuCommand: BitSet = BitSet(),
	/**
	 * 地面驾舱手柄上的按键状态
	 * <p>
	 * bit0：右手柄使能开关（常闭）<br>
	 * bit1：右手柄P2按钮（右上红）<br>
	 * bit2：右手柄P3按钮（左下绿）<br>
	 * bit3：右手柄P4按钮（右下红）<br>
	 * bit4：左手柄使能开关（常闭）<br>
	 * bit5：左手柄P2按钮（右上红）<br>
	 * bit6：左手柄P3按钮（左下绿）<br>
	 * bit7：左手柄P4按钮（右下红）<br>
	 */
	var buttonStatus: BitSet = BitSet(),
	/**
	 * 塔机当前控制模式 - 0:近控，1:远控
	 */
	var workMode: Int = 0,
	/**
	 * 塔机远控时心跳计数
	 */
	var sanyHeartbeat: Int = 0,
	/**
	 * 三一工作半径
	 */
	var sanyWorkRadius: Double = 0.0,
	/**
	 * 三一吊物重量
	 */
	var sanyLiftWeight: Double = 0.0,
	/**
	 * 三一吊钩离地高度
	 */
	var sanyHookHeightAboveGround: Double = 0.0,
	/**
	 * 塔吊是否启动
	 */
	var powerOn: Int = 0,
	/**
	 * 大臂倾角(IMU获取)
	 */
	var boomInclination: Double = 0.0,
	/**
	 * 吊钩滚筒震动
	 */
	val hookDrumVibration: Vibration = Vibration(),
	/**
	 * 大臂滚筒震动
	 */
	val boomDrumVibration: Vibration = Vibration(),
	/**
	 * 塔上钥匙状态
	 */
	var lockStatus: Int = 0,
	/**
	 * 是否触发挡位限制
	 */
	var gearLimit: Int = 0,
	/**
	 * 力矩百分比
	 */
	var torquePercentage: Double = 0.0,
	/**
	 * 限位1
	 */
	var limit1: BitSet = BitSet(),
	/**
	 * 限位2
	 */
	var limit2: BitSet = BitSet(),
	/**
	 * 预警
	 */
	var warn: BitSet = BitSet(),
	/**
	 * 起升变频器故障代码
	 */
	var liftError: Int = 0,
	/**
	 * 回转变频器故障代码
	 */
	var rotateError: Int = 0,
	/**
	 * 变幅变频器故障代码
	 */
	var amplitudeError: Int = 0,
	/**
	 * 急停信号
	 */
	var emergencyStop: Int = 0,
	/**
	 * ecu复位原因
	 */
	var ecuRestartReason: Int = 0,
	/**
	 * 座舱数据时间间隔
	 */
	var cockpitInterval: Long = 0,
	/**
	 * 塔机数据时间间隔
	 */
	var towerCraneInterval: Long = 0,
	/**
	 * 塔机CAN数据接收状态
	 */
	var canStatus: BitSet = BitSet(),
	/**
	 * ECU复位计数
	 */
	var ecuRestartCount: Int = 0,
	/**
	 * 从ECU收到挡位变化到塔机反馈对应挡位的执行时间
	 */
	var timeout1: Int = 0,
	/**
	 * 座舱挡位变化到塔机反馈对应挡位的执行时间
	 */
	var timeout2: Int = 0,
	/**
	 * 限控状态
	 */
	var limitControl: Int = 0,
	/**
	 * 蜗速状态
	 */
	var wormSpeed: Int = 0,
) {
	companion object {
		private val bufferHolder = ThreadLocal.withInitial { Buffer() }

		fun parse(bytes: ByteArray): EcuRealtimeData {
			val buffer = bufferHolder.get()!!.apply {
				clear()
				write(bytes)
			}
			val data = EcuRealtimeData()
			try {
				data.timestamp = buffer.readInt().toLong() and 0xFFFFFFFFL
				data.modeParent = buffer.readByte().toInt() and 0xFF
				data.modeChild = buffer.readByte().toInt() and 0xFF
				data.craneStatus = buffer.readByte().toInt() and 0xFF

				data.errorHigh = EcuDataParser.parseBitsFromLong(buffer.readLong())
				data.errorLow = EcuDataParser.parseBitsFromLong(buffer.readLong())
				data.instruction = EcuDataParser.parseBitsFromInt(buffer.readInt())
				data.remoteFunction = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())

				data.hookGear = buffer.readByte().toInt()
				data.trolleyGear = buffer.readByte().toInt()
				data.slewGear = buffer.readByte().toInt()

				data.craneSelfStatusHigh = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())
				data.craneSelfStatusLow = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())
				data.towerCraneErrorStatus = buffer.readByte().toInt() and 0xFF
				data.lightSignal = buffer.readByte().toInt() and 0xFF

				data.windDirection = (buffer.readShort().toInt() and 0xFFFF) / 10.0
				data.windSpeed = (buffer.readShort().toInt() and 0xFFFF) / 10.0
				data.temperature = buffer.readShort() / 10.0
				data.humidity = (buffer.readShort().toInt() and 0xFFFF) / 10.0
				data.averageWindSpeed = (buffer.readShort().toInt() and 0xFFFF) / 10.0

				data.hookHoistVibration.ax = buffer.readShort() / 100.0
				data.hookHoistVibration.ay = buffer.readShort() / 100.0
				data.hookHoistVibration.az = buffer.readShort() / 100.0
				data.trolleyHoistVibration.ax = buffer.readShort() / 100.0
				data.trolleyHoistVibration.ay = buffer.readShort() / 100.0
				data.trolleyHoistVibration.az = buffer.readShort() / 100.0

				data.workRadius = buffer.readInt() / 10_000.0
				data.towerInclination = buffer.readShort() / 100.0
				data.hookHeightAboveGround = buffer.readShort() / 100.0
				data.towerCranHeight = buffer.readInt() / 10_000.0
				data.loadingLimit = (buffer.readShort().toInt() and 0xFFFF) / 100.0
				data.radiusLimit = (buffer.readShort().toInt() and 0xFFFF) / 100.0
				data.towerCraneType = buffer.readByte().toInt() and 0xFF
				data.luffingAngle = buffer.readShort() / 100.0
				data.roll = buffer.readShort() / 100.0
				data.pitch = buffer.readShort() / 100.0
				data.yaw = buffer.readShort() / 100.0
				data.hookYaw = buffer.readShort() / 100.0

				data.hook1 = buffer.readInt() / 1_000.0
				data.hook2 = buffer.readInt() / 1_000.0
				data.ropeLength = buffer.readInt() / 1_000.0

				data.ecuCommand = EcuDataParser.parseBitsFromLong(buffer.readUint40())

				data.buttonStatus = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())
				data.workMode = buffer.readByte().toInt() and 0xFF
				data.sanyHeartbeat = buffer.readByte().toInt() and 0xFF
				data.sanyWorkRadius = (buffer.readShort().toInt() and 0xFFFF) / 100.0
				data.sanyLiftWeight = buffer.readInt() / 100.0
				data.sanyHookHeightAboveGround = buffer.readShort() / 100.0
				data.powerOn = buffer.readByte().toInt() and 0xFF
				data.boomInclination = buffer.readShort() / 100.0

				data.hookDrumVibration.ax = buffer.readShort() / 100.0
				data.hookDrumVibration.ay = buffer.readShort() / 100.0
				data.hookDrumVibration.az = buffer.readShort() / 100.0
				data.boomDrumVibration.ax = buffer.readShort() / 100.0
				data.boomDrumVibration.ay = buffer.readShort() / 100.0
				data.boomDrumVibration.az = buffer.readShort() / 100.0

				data.lockStatus = buffer.readByte().toInt() and 0xFF
				data.gearLimit = buffer.readByte().toInt() and 0xFF
				data.torquePercentage = (buffer.readShort().toInt() and 0xFFFF) / 10.0

				data.limit1 = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())
				data.limit2 = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())
				data.warn = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())

				data.liftError = buffer.readByte().toInt() and 0xFF
				data.rotateError = buffer.readByte().toInt() and 0xFF
				data.amplitudeError = buffer.readByte().toInt() and 0xFF
				data.emergencyStop = buffer.readByte().toInt() and 0xFF
				data.ecuRestartReason = buffer.readByte().toInt() and 0xFF

				data.cockpitInterval = buffer.readInt().toLong() and 0xFFFFFFFFL
				data.towerCraneInterval = buffer.readInt().toLong() and 0xFFFFFFFFL
				data.canStatus = EcuDataParser.parseBitsFromInt(buffer.readByte().toInt())
				data.ecuRestartCount = buffer.readByte().toInt() and 0xFF

				data.timeout1 = buffer.readShort().toInt()
				data.timeout2 = buffer.readShort().toInt()

				buffer.skip(3)//塔机反馈三轴挡位
				buffer.skip(8)//塔机反馈数据计数

				data.limitControl = buffer.readByte().toInt() and 0xFF
				data.wormSpeed = buffer.readByte().toInt() and 0xFF

			} catch (e: Exception) {
				Log.e("EcuRealtimeData", "data parse error: ${e.message}", e)
			}
			if (BuildConfig.DEBUG) {
				Logger.d("EcuRealtimeData", "parse result: $data")
			}
			return data
		}

		private fun Buffer.readUint40(): Long {
			var result = 0L
			repeat(5) {
				result = (result shl 8) or (this.readByte().toLong() and 0xFFL)
			}
			return result
		}
	}

	override fun toString(): String {
		return GsonUtils.toJson(this)
	}
}
