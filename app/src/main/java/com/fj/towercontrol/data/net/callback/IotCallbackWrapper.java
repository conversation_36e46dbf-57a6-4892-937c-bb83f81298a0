package com.fj.towercontrol.data.net.callback;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.GsonUtils;
import com.fj.towercontrol.data.net.dto.iot.IotApiResp;
import com.fjdynamics.app.logger.Logger;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;

/**
 * IOT平台通用回调处理
 *
 * <AUTHOR>
 */
public class IotCallbackWrapper<T> implements retrofit2.Callback<IotApiResp<T>> {
	private static final String TAG = "IotCallbackWrapper";
	private final Callback<T> callback;

	public IotCallbackWrapper(Callback<T> callback) {
		this.callback = callback;
	}

	@Override
	public void onResponse(
		@NonNull Call<IotApiResp<T>> call, @NonNull Response<IotApiResp<T>> response) {
		if (callback == null) {
			return;
		}
		if (!response.isSuccessful()) {
			try (ResponseBody body = response.errorBody()) {
				if (body == null) {
					callback.onError("", "数据解析异常");
					return;
				}

				Type type = new TypeToken<IotApiResp<Object>>() {
				}.getType();
				IotApiResp<Object> apiResp = GsonUtils.fromJson(body.string(), type);
				callback.onError(apiResp == null ? "" : apiResp.getCode(), apiResp == null ? "数据解析异常" : apiResp.getMessage());
			} catch (Exception e) {
				Logger.e(TAG, "iot response deserialization failed: " + e.getMessage());
			}
			return;
		}
		IotApiResp<T> apiResp = response.body();
		if (apiResp == null) {
			callback.onError("", "数据解析异常");
			return;
		}
		if (apiResp.getStatus() != 200) {
			callback.onError("", apiResp.getMessage());
			return;
		}
		callback.onSuccess(apiResp.getResult());
	}

	@Override
	public void onFailure(@NonNull Call<IotApiResp<T>> call, @NonNull Throwable t) {
		if (callback == null) {
			return;
		}
		callback.onError("", "网络异常");
	}
}
