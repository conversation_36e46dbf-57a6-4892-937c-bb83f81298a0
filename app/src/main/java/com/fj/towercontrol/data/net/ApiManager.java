package com.fj.towercontrol.data.net;

import com.blankj.utilcode.util.StringUtils;
import com.fj.towercontrol.BuildConfig;
import com.fj.towercontrol.TowerApp;
import com.fj.towercontrol.data.net.service.IotService;
import com.fj.towercontrol.data.net.service.OtaService;
import com.fj.towercontrol.data.net.service.PlatformService;
import com.fjdynamics.app.logger.Logger;

import java.util.concurrent.TimeUnit;

import me.jessyan.retrofiturlmanager.RetrofitUrlManager;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * 网络请求管理类
 *
 * <AUTHOR>
 */
public class ApiManager {

	private static final String TAG = "ApiManager";
	/**
	 * 塔吊平台接口
	 */
	public static final String DOMAIN_PLATFORM = "domain_platform";
	/**
	 * iot平台接口
	 */
	public static final String DOMAIN_IOT = "domain_iot";
	/**
	 * ota平台接口
	 */
	public static final String DOMAIN_OTA = "domain_ota";
	private final Retrofit retrofit;
	private PlatformService platformService;
	private IotService iotService;
	private OtaService otaService;

	private ApiManager() {
		HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(s -> Logger.d(TAG, s))
			.setLevel(BuildConfig.DEBUG ? HttpLoggingInterceptor.Level.BODY : HttpLoggingInterceptor.Level.HEADERS);
		OkHttpClient okHttpClient = RetrofitUrlManager.getInstance()
			.with(new OkHttpClient.Builder())
			// 添加鉴权请求头
//			.addInterceptor(new HeaderInterceptor())
			// 添加日志拦截器
			.addInterceptor(loggingInterceptor)
			.connectTimeout(10, TimeUnit.MINUTES)
			.readTimeout(10, TimeUnit.MINUTES)
			.writeTimeout(10, TimeUnit.MINUTES)
			.build();
		retrofit = new Retrofit.Builder()
			.client(okHttpClient)
			.baseUrl("http://192.168.32.28:8080/") // 这个baseUrl只是用来占位，真正请求的url根据request header动态配置
			.addConverterFactory(GsonConverterFactory.create())
			.build();
		refreshBaseUrl();
	}

	/**
	 * 刷新baseUrl
	 */
	public void refreshBaseUrl() {
		boolean isProd = TowerApp.IS_PROD;
		//塔吊后台接口baseUrl
		RetrofitUrlManager.getInstance()
			.putDomain(DOMAIN_PLATFORM, StringUtils.format("https://umtc-%s.fjdynamics.com/", isProd ? "hk" : "test"));
		//ota平台baseUrl
		RetrofitUrlManager.getInstance()
			.putDomain(DOMAIN_OTA, StringUtils.format("https://ota-%s.fjdac.%s/", isProd ? "hk" : "test", isProd ? "com" : "cn"));
		//iot平台baseUrl
		RetrofitUrlManager.getInstance()
			.putDomain(DOMAIN_IOT, StringUtils.format("https://iot-%s.fjdynamics.cn/", isProd ? "hk" : "test"));
	}

	/**
	 * 获取塔吊云平台服务接口
	 *
	 * @return PlatformService
	 */
	public PlatformService getPlatformService() {
		if (platformService == null) {
			platformService = retrofit.create(PlatformService.class);
		}
		return platformService;
	}

	/**
	 * 获取iot平台服务接口
	 *
	 * @return IotService
	 */
	public IotService getIotService() {
		if (iotService == null) {
			iotService = retrofit.create(IotService.class);
		}
		return iotService;
	}

	/**
	 * 获取OTA平台接口
	 *
	 * @return OtaService
	 */
	public OtaService getOtaService() {
		if (otaService == null) {
			otaService = retrofit.create(OtaService.class);
		}
		return otaService;
	}

	/**
	 * 获取ApiManager单例
	 *
	 * @return ApiManager单例
	 */
	public static ApiManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	private static final class SingletonHolder {
		public static final ApiManager INSTANCE = new ApiManager();
	}
}
