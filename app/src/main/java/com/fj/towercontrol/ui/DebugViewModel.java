package com.fj.towercontrol.ui;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.fj.fjprotocol.ProtocolConstants;
import com.fj.fjprotocol.TowerProtocol;
import com.fj.towercontrol.data.entity.SanyData;
import com.fj.towercontrol.data.entity.XCMGData;
import com.fj.towercontrol.data.entity.YongMaoData;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.ThreadExecutor;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.Logger;

/**
 * 三一塔吊ViewModel
 *
 * <AUTHOR>
 */
public class DebugViewModel extends ViewModel {
	private static final String TAG = "SanyViewModel";
	private final MutableLiveData<SanyData> ecuSanyStatusLiveData = new MutableLiveData<>();
	private final MutableLiveData<XCMGData> ecuXCMGLd = new MutableLiveData<>();
	private final MutableLiveData<YongMaoData> ecuYongMaoLd = new MutableLiveData<>();

	public LiveData<SanyData> getEcuSanyStatus() {
		return ecuSanyStatusLiveData;
	}

	public LiveData<XCMGData> getEcuXCMGData() {
		return ecuXCMGLd;
	}

	public LiveData<YongMaoData> getYongMaoData() {
		return ecuYongMaoLd;
	}

	public void handleMessageEvent(MessageEvent event) {
		if (event.getCode().equals(MessageEvent.CODE_ECU_DATA_RECEIVED)) {
			if (!(event.getData() instanceof TowerProtocol towerProtocol)) {
				Logger.e(TAG, "onMessageEvent:ecu data error");
				return;
			}
			handleEcuData(towerProtocol);
		}
	}

	private void handleEcuData(TowerProtocol towerProtocol) {
		ThreadExecutor.getInstance().executor(() -> {
			byte cmd = towerProtocol.getCmd();
			byte[] data = towerProtocol.getData();
			if (cmd == ProtocolConstants.DEBUG_STATUS_REPORT) {
				Logger.d(TAG, "接收到塔吊调试数据上报: " + DataUtil.byte2hex(data));
				if (data[0] == ProtocolConstants.SANY_STATUS_REPORT_TYPE) {
					SanyData sanyData = new SanyData(DataUtil.subBytes(data, 1, data.length - 1));
					ecuSanyStatusLiveData.postValue(sanyData);
				} else if (data[0] == ProtocolConstants.XCMD_STATUS_REPORT_TYPE) {
					XCMGData xcmgData = XCMGData.Companion.parse(DataUtil.subBytes(data, 1, data.length - 1));
					ecuXCMGLd.postValue(xcmgData);
				} else if (data[0] == ProtocolConstants.YONGMAO_STATUS_REPORT_TYPE) {
					YongMaoData yongMaoData = YongMaoData.Companion.parse(DataUtil.subBytes(data, 1, data.length - 1));
					ecuYongMaoLd.postValue(yongMaoData);
				}
			}
		});
	}

}
