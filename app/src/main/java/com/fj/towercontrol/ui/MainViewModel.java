package com.fj.towercontrol.ui;

import android.app.AlarmManager;
import android.app.Application;
import android.content.Context;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.fj.fjprotocol.ProtocolConstants;
import com.fj.fjprotocol.ProtocolHelper;
import com.fj.fjprotocol.TowerProtocol;
import com.fj.fjprotocol.TransManager;
import com.fj.fjprotocol.data.GeoData;
import com.fj.fjprotocol.ntrip.NtripManager;
import com.fj.towercontrol.TowerApp;
import com.fj.towercontrol.consts.Buttons;
import com.fj.towercontrol.consts.CraneType;
import com.fj.towercontrol.consts.MqttConstants;
import com.fj.towercontrol.data.entity.BatteryInfo;
import com.fj.towercontrol.data.entity.CargoWeightInfo;
import com.fj.towercontrol.data.entity.EcuLog;
import com.fj.towercontrol.data.entity.EcuRealtimeData;
import com.fj.towercontrol.data.entity.HistoryPoint;
import com.fj.towercontrol.data.entity.LightningData;
import com.fj.towercontrol.data.entity.MotorControlCmd;
import com.fj.towercontrol.data.entity.NtripInfo;
import com.fj.towercontrol.data.entity.PathPoint;
import com.fj.towercontrol.data.entity.RopeDetectionData;
import com.fj.towercontrol.data.entity.ScreenLog;
import com.fj.towercontrol.data.entity.SemiautomaticTask;
import com.fj.towercontrol.data.entity.SlowdownDelayConfig;
import com.fj.towercontrol.data.entity.TowerBaseConfig;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.data.entity.VoiceAlarmType;
import com.fj.towercontrol.data.net.callback.Callback;
import com.fj.towercontrol.data.net.dto.iot.RegisterDeviceResp;
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig;
import com.fj.towercontrol.data.net.dto.platform.Calibration;
import com.fj.towercontrol.data.net.dto.platform.SystemConfigDTO;
import com.fj.towercontrol.data.net.dto.platform.TowerConfigDto;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.modbus.HookModbusMaster;
import com.fj.towercontrol.modbus.VoiceModbusMaster;
import com.fj.towercontrol.mqtt.HiveMqClient;
import com.fj.towercontrol.mqtt.LidarMqClient;
import com.fj.towercontrol.mqtt.dto.InputsDTO;
import com.fj.towercontrol.mqtt.dto.PlatformDTO;
import com.fj.towercontrol.mqtt.entity.LidarDetectionData;
import com.fj.towercontrol.mqtt.entity.LoadingConfig;
import com.fj.towercontrol.mqtt.entity.PathControlCommand;
import com.fj.towercontrol.mqtt.entity.StatusControlCommand;
import com.fj.towercontrol.mqtt.entity.TowerData;
import com.fj.towercontrol.repository.RepositoryContainer;
import com.fj.towercontrol.tcp.HookLocationReceiver;
import com.fj.towercontrol.tcp.YongMaoDataRetriever;
import com.fj.towercontrol.util.Calculator;
import com.fj.towercontrol.util.CsvUtil;
import com.fj.towercontrol.util.EcuDataParser;
import com.fj.towercontrol.util.FileStore;
import com.fj.towercontrol.util.IntelliHookNmeaParser;
import com.fj.towercontrol.util.LogUploader;
import com.fj.towercontrol.util.MemoryStore;
import com.fj.towercontrol.util.OtaManager;
import com.fj.towercontrol.util.TaskHistorySender;
import com.fj.towercontrol.util.ThreadExecutor;
import com.fj.towercontrol.websocket.ErrorCode;
import com.fj.towercontrol.websocket.WebSocketMessage;
import com.fj.towercontrol.websocket.client.LargeScreenWebSocketManager;
import com.fj.towercontrol.websocket.client.dto.LargeScreenConfigChangeDTO;
import com.fj.towercontrol.websocket.client.dto.TaskUpdateDTO;
import com.fj.towercontrol.websocket.server.Messenger;
import com.fj.towercontrol.websocket.server.dto.LoginStatusUpdateDTO;
import com.fjd.app.common.util.DataUtil;
import com.fjdynamics.app.logger.EcuStrLogger;
import com.fjdynamics.app.logger.Logger;
import com.google.gson.reflect.TypeToken;

import org.json.JSONObject;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.lang.reflect.Type;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;

/**
 * 主界面ViewModel
 *
 * <AUTHOR>
 */
public class MainViewModel extends AndroidViewModel {

	private static final String TAG = "MainViewModel";

	/**
	 * 当前ws客户端与大屏ws服务连接状态
	 */
	private Boolean currWsStatus;
	/**
	 * 当前ws客户端与ai-lift ws服务连接状态
	 */
	private Boolean currAiLiftWsStatus;
	/**
	 * 任务暂停时的吊钩位置
	 */
	private GeoData pausedHookPosition;
	/**
	 * A->B历史轨迹
	 */
//	private final List<HistoryPoint> historyPointList = new LimitedArrayList<>(2_000);
	/**
	 * ecu心跳定时器
	 */
	private final Timer ecuHeartbeatTimer = new Timer();
	private final EcuHeartbeatTask ecuHeartbeatTask = new EcuHeartbeatTask();
	private final Handler mainHandler = new Handler(Looper.getMainLooper());
	private final LinkedList<ScreenLog> logQueue = new LinkedList<>();
	/**
	 * ecu版本号信息
	 */
	private final MutableLiveData<String> ecuVersionLiveData = new MutableLiveData<>();
	/**
	 * ecu实时状态信息
	 */
	private final MutableLiveData<EcuRealtimeData> ecuRealtimeDataLd = new MutableLiveData<>();
	/**
	 * ecu日志上报信息
	 */
	private final MutableLiveData<EcuLog> ecuLogLiveData = new MutableLiveData<>();
	/**
	 * 左侧屏幕日志
	 */
	private final MutableLiveData<LinkedList<ScreenLog>> screenLogLiveData = new MutableLiveData<>(logQueue);
	/**
	 * 称重数据信息
	 */
	private final MutableLiveData<CargoWeightInfo> cargoWeightLiveData = new MutableLiveData<>();
	/**
	 * 电池数据信息
	 */
	private final MutableLiveData<BatteryInfo> batteryInfoLiveData = new MutableLiveData<>();
	//	private final MutableLiveData<TowerCraneData> towerCranDataLd = new MutableLiveData<>();
	private final MutableLiveData<LidarDetectionData> lidarDataLd = new MutableLiveData<>();
	private final MutableLiveData<LightningData> lightningDataLd = new MutableLiveData<>();
	private final MutableLiveData<RopeDetectionData> ropeDetectionDataLd = new MutableLiveData<>();

	private Boolean mqttConnected;
	private int prevMotorButtonStatus;
	private Boolean currSoundAlarm;
	private Boolean currLightAlarm;
	private Boolean currPersonIn3M;
	private long lidarDataUpdateTimestamp = 0L;

	public LiveData<String> getEcuVersion() {
		return ecuVersionLiveData;
	}

	public LiveData<EcuRealtimeData> getEcuRealtimeData() {
		return ecuRealtimeDataLd;
	}

	public LiveData<EcuLog> getEcuLog() {
		return ecuLogLiveData;
	}

	public LiveData<LinkedList<ScreenLog>> getScreenLog() {
		return screenLogLiveData;
	}

	public LiveData<CargoWeightInfo> getCargoWeight() {
		return cargoWeightLiveData;
	}

	public LiveData<BatteryInfo> getBatteryInfo() {
		return batteryInfoLiveData;
	}
//	public LiveData<TowerCraneData> getTowerCraneData() {
//		return towerCranDataLd;
//	}

	public LiveData<LidarDetectionData> getLidarData() {
		return lidarDataLd;
	}

	public LiveData<LightningData> getLightningData() {
		return lightningDataLd;
	}

	public LiveData<RopeDetectionData> getRopeDetectionData() {
		return ropeDetectionDataLd;
	}

	public MainViewModel(@NonNull Application application) {
		super(application);
		Logger.i(TAG, "init");
		mainHandler.postDelayed(() -> {
			// 查询ecu版本
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.queryEcuVersion());
		}, 1_000);
		// 定时发送ecu心跳
		ecuHeartbeatTimer.schedule(ecuHeartbeatTask, 0, 1_000);
		// 开启本地TcpServer接收吊钩、小车和毫米波雷达数据
		HookLocationReceiver.getInstance().start();
		HookModbusMaster.getInstance().start();
		VoiceModbusMaster.getInstance().start();
//		LightningModbusMaster.getInstance().start();
//		RopeDetectionModbusMaster.getInstance().start();
		int currCraneType = MemoryStore.getInstance().getSystemConfig().getCraneType();
		if (CraneType.fromOrdinal(currCraneType) == CraneType.YONGMAO_STT153) {
			YongMaoDataRetriever.INSTANCE.connect();
		}
		addMessage("系统时间同步中");
		startSyncSystemTime();

		mainHandler.postDelayed(() -> TowerApp.getWebSocketServer().start(), 2_000);
	}

	private void startSyncSystemTime() {
		ThreadExecutor.getInstance().executor(() -> {
			try {
				Logger.d(TAG, "startSyncSystemTime: start");
				URL url = new URL("http://www.baidu.com");
				URLConnection conn = url.openConnection();
				conn.setConnectTimeout(10_000);
				conn.connect();
				long timestamp = conn.getDate();
				Logger.d(TAG, "startSyncSystemTime: success, timestamp: " + timestamp + ", formatted: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date(timestamp)));
				AlarmManager alarmManager = (AlarmManager) getApplication().getSystemService(Context.ALARM_SERVICE);
				alarmManager.setTime(timestamp);
				addMessage("系统时间同步成功");
				initNtrip();
				queryFromServer();
				LargeScreenWebSocketManager.getInstance().connect();
//				AiLiftWebSocketManager.getInstance().connect();
				HiveMqClient.INSTANCE.connect();
				LidarMqClient.INSTANCE.connect();
				registerDevice();
			} catch (Exception e) {
				Logger.e(TAG, "startSyncSystemTime exception: system time sync failed: " + e.getMessage(), e);
				SystemClock.sleep(1_500);
				startSyncSystemTime();
			}
		});
	}

	private void initNtrip() {
		try {
			NtripInfo ntripInfo = FileStore.getNtripCustomize();
			if (ntripInfo != null
				&& ntripInfo.getUserInfo() != null
				&& ntripInfo.getSourcePoint() != null
				&& ntripInfo.getAddressInfo() != null) {
				NtripManager.getInstance().setSourcePoint(ntripInfo.getSourcePoint());
				NtripManager.getInstance().setAddressInfo(ntripInfo.getAddressInfo());
				NtripManager.getInstance().setUserInfo(ntripInfo.getUserInfo());
				boolean ntripDisable = FileStore.getNtripDisabled();
				if (ntripDisable) {
					NtripManager.getInstance().setNtripSourceState(NtripManager.NtripState.DISABLE);
				}
				if (!TextUtils.isEmpty(ntripInfo.getSourcePoint())) {
					NtripManager.getInstance().linkSource();
				} else {
					NtripManager.getInstance().getSource();
				}
			}
			if (NtripManager.getInstance().getUserInfo() != null
				&& NtripManager.getInstance().getSourcePoint() != null
				&& NtripManager.getInstance().getAddressInfo() != null) {
				NtripManager.getInstance().linkSource();
			}
		} catch (Exception e) {
			Logger.e("APP", e.getMessage());
		}
	}

	private void queryFromServer() {
		queryAlarmConfigList();
		queryTowerConfig();
		querySystemConfig();
	}

	/**
	 * 查询云端预警规则配置并更新到本地
	 */
	private void queryAlarmConfigList() {
		RepositoryContainer.getInstance()
			.getPlatformRepository()
			.queryPlatformAlarmConfig(new Callback<>() {
				@Override
				public void onSuccess(List<AlarmConfig> alarmConfigs) {
					Logger.d(TAG, "queryPlatformAlarmConfig onSuccess: " + alarmConfigs);
					FileStore.setAlarmConfigList(alarmConfigs);
					addMessage("预警配置更新成功");
					updateEcuAlarmConfig();
				}

				@Override
				public void onError(String code, String msg) {
					Logger.e(TAG, "queryPlatformAlarmConfig onError: code -> " + code + ", msg -> " + msg);
					addMessage("预警配置更新失败");
					updateEcuAlarmConfig();
				}
			});
	}

	/**
	 * 查询云端塔吊属性配置并更新到本地
	 */
	private void queryTowerConfig() {
		RepositoryContainer.getInstance()
			.getPlatformRepository()
			.queryPlatformTowerConfig(new Callback<>() {
				@Override
				public void onSuccess(List<TowerConfigDto> towerConfigDtoList) {
					addMessage("塔吊配置更新成功");
					Logger.d(TAG, "queryPlatformTowerConfig onSuccess: " + towerConfigDtoList);
					TowerConfig towerConfig = parseTowerConfig(towerConfigDtoList);
					FileStore.setTowerConfig(towerConfig);
					updateEcuTowerConfig(towerConfig);
				}

				@Override
				public void onError(String code, String msg) {
					Logger.e(TAG, "queryPlatformTowerConfig onError: code -> " + code + ", msg -> " + msg);
					addMessage("塔吊配置更新失败");
					//配置更新失败时，使用缓存的配置
					updateEcuTowerConfig(FileStore.getTowerConfig());
				}
			});
	}

	private void querySystemConfig() {
		RepositoryContainer.getInstance()
			.getPlatformRepository()
			.querySystemConfigList(new Callback<>() {
				@Override
				public void onSuccess(List<SystemConfigDTO> systemConfig) {
					addMessage("系统配置更新成功");
					try {
						for (SystemConfigDTO config : systemConfig) {
							if (TextUtils.equals(config.getConfigKey(), "limit_control_by_333")) {
								boolean limitBy333 = (Integer.parseInt(config.getConfigValue()) > 0);
								FileStore.setLimitControlBy333(limitBy333);
								boolean needLimit333 = limitBy333 && currPersonIn3M != null && currPersonIn3M;
								TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.update333Alarm(needLimit333));
								addMessage("333是否限控: " + needLimit333);
							}
						}
					} catch (Exception e) {
						Logger.e(TAG, "querySystemConfig: config parse exception: " + e.getMessage());
					}
				}

				@Override
				public void onError(String code, String msg) {
					Logger.e(TAG, "querySystemConfig onError: code -> " + code + ", msg -> " + msg);
					addMessage("系统配置更新失败");
				}
			});
	}

	private TowerConfig parseTowerConfig(List<TowerConfigDto> towerConfigDtoList) {
		if (CollectionUtils.isEmpty(towerConfigDtoList)) {
			return null;
		}
		TowerConfig towerConfig = new TowerConfig();
		for (TowerConfigDto towerConfigDto : towerConfigDtoList) {
			if (TextUtils.equals(towerConfigDto.getPropertyName(), "base")) {
				//基本属性配置
				TowerBaseConfig towerBaseConfig = GsonUtils.fromJson(towerConfigDto.getPropertyValue(), TowerBaseConfig.class);
				towerConfig.setTowerBaseConfig(towerBaseConfig);
			} else if (TextUtils.equals(towerConfigDto.getPropertyName(), "radius_weight_limit")) {
				//限重配置
				Type type = new TypeToken<List<LoadingConfig>>() {
				}.getType();
				List<LoadingConfig> loadingConfigList = GsonUtils.fromJson(towerConfigDto.getPropertyValue(), type);
				towerConfig.setLoadCapacityChart(loadingConfigList);
			} else if (TextUtils.equals(towerConfigDto.getPropertyName(), "calibration")) {
				//校准配置
				Calibration calibration = GsonUtils.fromJson(towerConfigDto.getPropertyValue(), Calibration.class);
				towerConfig.setCalibration(calibration);
			}
		}
		return towerConfig;
	}

	@Override
	protected void onCleared() {
		Logger.i(TAG, "onCleared");
		// 停止ecu心跳
		ecuHeartbeatTask.cancel();
		ecuHeartbeatTimer.cancel();
		HookLocationReceiver.getInstance().stop();
		HookModbusMaster.getInstance().stop();
		VoiceModbusMaster.getInstance().stop();
//		LightningModbusMaster.getInstance().stop();
//		RopeDetectionModbusMaster.getInstance().stop();
		YongMaoDataRetriever.INSTANCE.disconnect();
		HiveMqClient.INSTANCE.disconnect();
		LidarMqClient.INSTANCE.disconnect();
		super.onCleared();
	}

	public void handleMessageEvent(MessageEvent event) {
		switch (event.getCode()) {
			case MessageEvent.CODE_ECU_DATA_RECEIVED:
				if (!(event.getData() instanceof TowerProtocol towerProtocol)) {
					Logger.e(TAG, "onMessageEvent: ecu data error");
					return;
				}
				handleEcuData(towerProtocol);
				break;
			case MessageEvent.CODE_IOT_MESSAGE_RECEIVED:
				if (!(event.getData() instanceof String iotJson)) {
					Logger.e(TAG, "onMessageEvent: iot data error");
					return;
				}
				PlatformDTO platformDTO = GsonUtils.fromJson(iotJson, PlatformDTO.class);
				handleIotMessage(platformDTO);
				break;
			case MessageEvent.CODE_CLIENT_WS_MSG_RECEIVED: {
				if (!(event.getData() instanceof String json)) {
					Logger.e(TAG, "onMessageEvent: pad data error");
					return;
				}
				Type type = new TypeToken<WebSocketMessage<Object>>() {
				}.getType();
				WebSocketMessage<Object> webSocketMessage = GsonUtils.fromJson(json, type);
				handlePadMessage(webSocketMessage.getMessageType(), webSocketMessage.getMessageId(), json);
				break;
			}
			case MessageEvent.CODE_BOARD_CARD_CONFIG_FINISHED:
				addMessage("板卡配置结束");
				break;
			case MessageEvent.CODE_CARGO_WEIGHT_EVENT:
				Object cargoWeightInfo = event.getData();
				if (cargoWeightInfo instanceof CargoWeightInfo) {
					cargoWeightLiveData.setValue((CargoWeightInfo) cargoWeightInfo);
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setCurrentLoad((((CargoWeightInfo) cargoWeightInfo).getNetWeight())));
				} else {
					cargoWeightLiveData.setValue(null);
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setCurrentLoad(0));
				}
				break;
			case MessageEvent.SERVER_URL_UPDATED:
				queryFromServer();
				break;
			case MessageEvent.SERVER_WS_STATUS_UPDATED:
				boolean wsStatus = (boolean) event.getData();
				if (currWsStatus == null || currWsStatus != wsStatus) {
					if (wsStatus) {
						addMessage("已连接到中控机");
					} else {
						addMessage("已断开中控机连接");
					}
					currWsStatus = wsStatus;
				}
				break;
			case MessageEvent.AI_LIFT_WS_STATUS_UPDATED:
				boolean aiLiftWsStatus = (boolean) event.getData();
				if (currAiLiftWsStatus == null || currAiLiftWsStatus != aiLiftWsStatus) {
					if (aiLiftWsStatus) {
						addMessage("已连接到AI-LIFT");
					} else {
						addMessage("已断开AI-LIFT连接");
					}
					currAiLiftWsStatus = aiLiftWsStatus;
				}
				break;
			case MessageEvent.BATTERY_INFO_UPDATED: {
				Object data = event.getData();
				if (!(data instanceof BatteryInfo)) {
					MemoryStore.getInstance().setBatteryInfo(null);
					batteryInfoLiveData.postValue(null);
					return;
				}
				MemoryStore.getInstance().setBatteryInfo((BatteryInfo) data);
				batteryInfoLiveData.postValue((BatteryInfo) data);
				break;
			}
//			case MessageEvent.CODE_CRANE_DATA_UPDATE: {
//				Object data = event.getData();
//				if (data instanceof TowerCraneData) {
//					TowerCraneData craneData = (TowerCraneData) data;
//					MemoryStore.getInstance().setTowerCraneData(craneData);
//					towerCranDataLd.postValue(craneData);
//				} else {
//					MemoryStore.getInstance().setTowerCraneData(null);
//					towerCranDataLd.postValue(null);
//				}
//				break;
//			}
			case MessageEvent.SERVER_WS_MESSAGE: {
				//收到大屏ws消息
				Object data = event.getData();
				if (!(data instanceof String)) {
					return;
				}
				handleLargeScreenData((String) data, 0);
				break;
			}
			case MessageEvent.AI_LIFT_WS_MESSAGE: {
				//收到AI-LIFT ws消息
				Object data = event.getData();
				if (!(data instanceof String)) {
					return;
				}
				handleLargeScreenData((String) data, 1);
				break;
			}
			case MessageEvent.CODE_LIDAR_DETECTION_UPDATE: {
				//激光雷达数据更新消息
				Object data = event.getData();
				if (!(data instanceof LidarDetectionData lidarData)) {
					return;
				}
				handleLidarData(lidarData);
				break;
			}
			case MessageEvent.CODE_LIDAR_NMEA_DATA_UPDATE: {
				Object data = event.getData();
				if (!(data instanceof byte[] nmeaBytes)) {
					return;
				}
				TransManager.getInstance().sendEcuDataWithUart6(ProtocolHelper.sendHookGgaData(nmeaBytes));
				IntelliHookNmeaParser.INSTANCE.addNmeaData(nmeaBytes);
				break;
			}
			case MessageEvent.CODE_LIGHTNING_DATA_UPDATE: {
				if (!(event.getData() instanceof LightningData lightningData)) {
					MemoryStore.getInstance().setLightningData(null);
					return;
				}
				MemoryStore.getInstance().setLightningData(lightningData);
				lightningDataLd.postValue(lightningData);
				break;
			}
			case MessageEvent.CODE_ROPE_DETECTION_DATA_UPDATE:
				if (!(event.getData() instanceof RopeDetectionData ropeDetectionData)) {
					return;
				}
				ropeDetectionDataLd.postValue(ropeDetectionData);
				break;
			default:
				break;
		}
	}

	private void handleLidarData(LidarDetectionData lidarData) {
		ThreadExecutor.getInstance().executor(() -> {
			long currentTimestamp = SystemClock.elapsedRealtime();
			if (currentTimestamp - lidarDataUpdateTimestamp >= 1_000) {
				//雷达数据UI层刷新频率限制最多1hz
				lidarDataUpdateTimestamp = currentTimestamp;
				lidarDataLd.postValue(lidarData);
			}
			LargeScreenWebSocketManager.getInstance().sendData(lidarData.toObstaclesDTO());
		});
	}

	/**
	 * 处理服务端的ws消息
	 *
	 * @param json   json
	 * @param source 0:UMTC大屏, 1:AI-LIFT
	 */
	private void handleLargeScreenData(String json, int source) {
		ThreadExecutor.getInstance().executor(() -> {
			try {
				JSONObject jo = new JSONObject(json);
				String type = jo.getString("type");
				String sourceStr = source == 0 ? "UMTC" : "AI-LIFT";
//				if (TextUtils.equals(type, "radiusUpdate")) {
//					//吊物半径更新
//					double loadRadius = jo.getDouble("loadRadius");
//					addMessage("接收到" + sourceStr + "吊物半径更新消息: " + loadRadius);
//					FileStore.setCargoRadius(loadRadius);
//				} else
				if (TextUtils.equals(type, "alarmTrigger")) {
					//触发声光告警
					boolean light = jo.optBoolean("light");
					boolean sound = jo.optBoolean("sound");
					boolean personIn3M = jo.optBoolean("personIn3mArea");
					if (currLightAlarm == null
						|| currSoundAlarm == null
						|| currPersonIn3M == null
						|| currLightAlarm != light
						|| currSoundAlarm != sound
						|| currPersonIn3M != personIn3M) {
						addMessage("接收到" + sourceStr + "消息,灯: " + light + ", 声: " + sound + ", 3m圈: " + personIn3M);
						currLightAlarm = light;
						currSoundAlarm = sound;
						currPersonIn3M = personIn3M;

						boolean needLimit333 = MemoryStore.getInstance().isLimitControlBy333() && personIn3M;
						TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.update333Alarm(needLimit333));
					}
//					AlarmIOModbusReader.getInstance().controlIO(light, sound);
					if (sound || light) {
						int voiceAlarmType = jo.optInt("voice");
						VoiceModbusMaster.getInstance().playVoice(VoiceAlarmType.fromVoiceType(voiceAlarmType));
					}
				} else if (TextUtils.equals(type, "motorCtrl")) {
					EcuRealtimeData ecuRealtimeData = ecuRealtimeDataLd.getValue();
					if (ecuRealtimeData == null) {
						return;
					}
					if (ecuRealtimeData.getWorkMode() == 0) {
						//近控时umtc不控制舵机，由ai-lift控制
						return;
					}
					int cmd = jo.getInt("cmd");
					if (cmd == 0) {
						broadcastToTowerPad(Messenger.sendMotorControlCmd(MotorControlCmd.STOP));
					} else if (cmd == 1) {
						broadcastToTowerPad(Messenger.sendMotorControlCmd(MotorControlCmd.FORWARD));
					} else if (cmd == -1) {
						broadcastToTowerPad(Messenger.sendMotorControlCmd(MotorControlCmd.BACKWARD));
					}
				}
			} catch (Exception e) {
				//大屏回复"OK:timestamp"时会导致json序列化异常,不用处理
			}
		});
	}

	private void handleEcuData(TowerProtocol towerProtocol) {
		byte cmd = towerProtocol.getCmd();
		byte[] data = towerProtocol.getData();
		switch (cmd) {
			case ProtocolConstants.DISCRETE_COMMAND:
				// 接收到离散指令回复
				Logger.d(TAG, "接收到离散指令回复: " + DataUtil.byte2hex(data));
				decodeEcuDiscreteResponse(data);
				break;
			case ProtocolConstants.ECU_REALTIME_STATUS:
				// ecu实时状态上报
				Logger.d(TAG, "接收到ecu实时状态上报: " + DataUtil.byte2hex(data));
				decodeEcuRealtimeData(data);
				break;
			case ProtocolConstants.ECU_BUTTON_STATUS:
				decodeEcuButtonStatus(data);
				break;
			case ProtocolConstants.ECU_LOG:
				// ecu日志上报
				Logger.d(TAG, "接收到ecu日志上报: " + DataUtil.byte2hex(data));
				decodeEcuLog(data);
				break;
			case ProtocolConstants.ECU_REQUEST_SEMIAUTOMATIC_START:
				int type = data[0] & 0xff;
				Logger.d(TAG, "接收到ecu请求开始半自动吊运: " + type);
				addMessage("接收到ECU请求开始半自动吊运：" + type);
				handleRetracingTask(type);
				break;
			case ProtocolConstants.OTA_CMD:
				Logger.d(TAG, "收到ecu OTA消息: " + DataUtil.byte2hex(data));
				OtaManager.getInstance().handleEcuResponse(data);
				break;
			case ProtocolConstants.ECU_RESTART:
				Logger.d(TAG, "接收到ecu复位消息");
				addMessage("接收到ecu复位消息");
				handleEcuRestart();
				break;
			case ProtocolConstants.ECU_STR_LOG:
				saveEcuStrLog(data);
				break;
			default:
				Log.w(TAG, String.format("接收到未定义的ecu消息: cmd -> %s, data -> %s", DataUtil.byte2hex(towerProtocol.getCmd()), DataUtil.byte2hex(data)));
				break;
		}
	}

	private void saveEcuStrLog(byte[] logBytes) {
		ThreadExecutor.getInstance().executor(() -> {
			String ecuLog = new String(logBytes);
			if (TextUtils.isEmpty(ecuLog)) {
				return;
			}
			EcuStrLogger.d("", ecuLog);
		});
	}

	private void handleEcuRestart() {
		ThreadExecutor.getInstance().executor(() -> {
			TransManager.getInstance().setUpgrading(false);
			//先查询一下ecu版本号
			SystemClock.sleep(50);
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.queryEcuVersion());
			//发送登录状态
			SystemClock.sleep(50);
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateDriverLoginStatus(MemoryStore.getInstance().getLoginStatus() == 1));
			//发送塔上切断钥匙和信号灯状态
			SystemClock.sleep(50);
			Boolean cutoff = MemoryStore.getInstance().getCutoff();
			Boolean lightOn = MemoryStore.getInstance().getLightOn();
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateCutoffAndLightSignal(cutoff != null && cutoff, lightOn != null && lightOn));
			//下发ecu配置
			SystemClock.sleep(50);
			updateEcuTowerConfig(FileStore.getTowerConfig());
			//下发预警配置
			SystemClock.sleep(50);
			updateEcuAlarmConfig();
		});
	}

	private void decodeEcuButtonStatus(byte[] data) {
		ThreadExecutor.getInstance().executor(() -> {
			List<Integer> buttons = EcuDataParser.parseEcuButtonStatus(data);
			List<Integer> prevButtons = MemoryStore.getInstance().getButtons();
//			Logger.d(TAG, "decodeEcuButtonStatus: " + buttons);
			if (buttons != null && !buttons.equals(prevButtons)) {
				Logger.d(TAG, "buttons changed: " + buttons);
				broadcastToTowerPad(Messenger.reportButtonStatus(buttons));
				MemoryStore.getInstance().setButtons(buttons);
				EcuRealtimeData ecuRealtimeData = ecuRealtimeDataLd.getValue();
				if (ecuRealtimeData == null) {
					return;
				}
				if (ecuRealtimeData.getWorkMode() == 0) {
					//近控时umtc不控制舵机，由ai-lift控制
					return;
				}
				if (buttons.contains(Buttons.UP) && prevMotorButtonStatus != Buttons.UP) {
					//执行向上
					broadcastToTowerPad(Messenger.sendMotorControlCmd(MotorControlCmd.FORWARD));
					prevMotorButtonStatus = Buttons.UP;
				} else if (buttons.contains(Buttons.DOWN) && prevMotorButtonStatus != Buttons.DOWN) {
					//执行向下
					broadcastToTowerPad(Messenger.sendMotorControlCmd(MotorControlCmd.BACKWARD));
					prevMotorButtonStatus = Buttons.DOWN;
				} else if (!buttons.contains(Buttons.UP) && !buttons.contains(Buttons.DOWN)) {
					//取消
					broadcastToTowerPad(Messenger.sendMotorControlCmd(MotorControlCmd.STOP));
					prevMotorButtonStatus = 0;
				}
			}
		});
	}

	/**
	 * 解析ecu实时状态上报
	 *
	 * @param data 消息体报文
	 */
	private void decodeEcuRealtimeData(byte[] data) {
		ThreadExecutor.getInstance().executor(() -> {
			// 将ecu数据解析成对象
//			EcuRealtimeStatus ecuRealtimeStatus = EcuDataParser.parseEcuRealtimeStatus(data);
			EcuRealtimeData ecuData = EcuRealtimeData.Companion.parse(data);
			ecuRealtimeDataLd.postValue(ecuData);
			EcuLog ecuLog = ecuLogLiveData.getValue();

			int pathCollectStatus = MemoryStore.getInstance().getPathCollectStatus();
			if (ecuLog != null && pathCollectStatus == MemoryStore.COLLECT_STATUS_COLLECTING) {
				// 采集A->B历史轨迹
				HistoryPoint point = new HistoryPoint(
					System.currentTimeMillis(),
					ecuLog.getHookLocation().getLat(),
					ecuLog.getHookLocation().getLng(),
					ecuLog.getHookLocation().getAlt(),
					ecuData.getHookGear(),
					ecuData.getTrolleyGear(),
					ecuData.getSlewGear()
				);
//				historyPointList.add(point);
				MemoryStore.getInstance().getHistoryPointList().add(point);
			}

			// ecu解析出来的数据转换成上报iot平台的数据
			TowerData towerData = TowerData.build(ecuData, ecuLog, cargoWeightLiveData.getValue());

			//塔吊属性上报IOT
			HiveMqClient.INSTANCE.reportTowerData(towerData);
			//塔吊属性同步上报中控大屏
			LargeScreenWebSocketManager.getInstance().sendData(towerData);
//			AiLiftWebSocketManager.getInstance().sendData(towerData);
			//塔吊属性透传给司机中控
			broadcastToTowerPad(Messenger.reportTowerData(towerData));
		});
	}

	/**
	 * 解析ecu日志上报
	 *
	 * @param data 消息体报文
	 */
	private void decodeEcuLog(byte[] data) {
		ThreadExecutor.getInstance().executor(() -> {
			EcuLog ecuLog = EcuDataParser.parseLog(data);
			ecuLogLiveData.postValue(ecuLog);
		});
	}

	/**
	 * 解析ecu离散指令响应
	 *
	 * @param data data
	 */
	private void decodeEcuDiscreteResponse(byte[] data) {
		ThreadExecutor.getInstance().executor(() -> {
			byte commandId = data[0];
			switch (commandId) {
				case ProtocolConstants.ECU_VERSION:
					// ecu版本回复
					String ecuVersion = EcuDataParser.parseVersion(DataUtil.subBytes(data, 1, data.length - 1));
					Logger.d(TAG, "接收到ecu版本回复 -> " + ecuVersion);
					ecuVersionLiveData.postValue(String.format("ECU版本：%s", ecuVersion));
					if (!TextUtils.equals(ecuVersion, FileStore.getEcuVersion())) {
						// 如果ecu版本发生变化，将新版本保存到本地并上报iot平台
//						MqttClient.getInstance().reportVersion(BuildConfig.VERSION_NAME, ecuVersion);
						HiveMqClient.INSTANCE.reportVersionInfo();
					}
					FileStore.setEcuVersion(ecuVersion);
					break;
				case ProtocolConstants.SEND_HISTORY_RESPONSE:
					// 轨迹下发回复
					byte[] bytes = DataUtil.subBytes(data, 1, data.length - 1);
					int result = bytes[2] & 0xff;
					// ecu上一次接收到的起始点位index
					int ecuLastReceivedIndex = DataUtil.bytes2Int(bytes[0], bytes[1]);
					Logger.d(TAG, "接收到ecu轨迹下发回复：result -> " + result + ", lastReceiveIndex -> " + ecuLastReceivedIndex);
					TaskHistorySender.getInstance().onEcuResponse(result, ecuLastReceivedIndex);
					break;
				case ProtocolConstants.SET_ARM:
					addMessage("向ECU设置吊臂长度及远近端安全距离成功");
					break;
				case ProtocolConstants.SET_OBSTACLE_POINTS:
					addMessage("向ECU设置设置障碍物成功");
					break;
				case ProtocolConstants.SET_CARGO_SIZE:
					addMessage("向ECU设置吊物尺寸成功");
					break;
				case ProtocolConstants.SET_OBSTACLE_WARN:
					addMessage("向ECU设置避障预警成功");
					break;
				case ProtocolConstants.SET_WIND_SPEED_WARN:
					addMessage("向ECU设置风速预警成功");
					break;
				case ProtocolConstants.SET_LOCATION_C:
					addMessage("向ECU设置塔基位置成功");
					break;
				case ProtocolConstants.SET_LOAD_WARN_TABLE:
					addMessage("向ECU设置负载预警表成功");
					break;
				case ProtocolConstants.SET_CURRENT_LOAD:
					// 打印太频繁，先注释掉
					//                    runOnUiThread(() ->
					// screenLogLiveData.setValue("向ECU设置当前负载成功"));
					break;
				case ProtocolConstants.SET_LOCATION_A:
					addMessage("向ECU设置A点位置成功");
					break;
				case ProtocolConstants.SET_LOCATION_B:
					addMessage("向ECU设置B点位置成功");
					break;
				case ProtocolConstants.SET_TROLLEY_WIDTH:
					addMessage("向ECU设置小车宽度成功");
					break;
				case ProtocolConstants.SET_ALARM_CONFIG:
					addMessage("向ECU设置预警规则成功");
					break;
				case ProtocolConstants.SET_INCLINATION_OFFSET:
					addMessage("向ECU设置倾角校准值成功");
					break;
				case ProtocolConstants.UPDATE_DRIVER_LOGIN_STATUS:
					addMessage("向ECU更新机手登录状态成功");
					break;
				case ProtocolConstants.CONFIG_SLOWDOWN_DELAY:
					addMessage("接收到ECU缓停配置回复");
					break;
				case ProtocolConstants.SET_SEMIAUTOMATIC_HEIGHT:
					addMessage("向ECU设置半自动安全高度成功");
					break;
				case ProtocolConstants.START_SEMIAUTOMATIC_TASK_RESP:
					int ret = data[1] & 0xff;
					addMessage("ecu半自动吊运状态更新：" + ret);
					LargeScreenWebSocketManager.getInstance().sendData(new TaskUpdateDTO(ret));
					broadcastToTowerPad(Messenger.updateSemiAutoTaskStatus("", ret));
					break;
				case ProtocolConstants.UPDATE_CUTOFF_AND_LIGHT_SIGNAL:
					addMessage("向ECU设置钥匙信号成功");
					break;
				default:
					Logger.d(TAG, "接收到其他离散指令回复, commandId: " + DataUtil.byte2hex(commandId));
					break;
			}
		});
	}

	/**
	 * 处理IOT平台下发消息
	 *
	 * @param platformDTO 平台消息实体类
	 */
	private void handleIotMessage(PlatformDTO platformDTO) {
		ThreadExecutor.getInstance().executor(() -> {
			String function = platformDTO.getFunctionId();
			List<InputsDTO> inputs = platformDTO.getInputs();
			switch (function) {
				case MqttConstants.FUNCTION_GET_LOG: {
					// 日志拉取
					int logType = -1;
					for (InputsDTO input : inputs) {
						if (TextUtils.equals(input.getName(), "command")) {
							if (input.getValue() instanceof String) {
								logType = Integer.parseInt((String) input.getValue());
								break;
							}
						}
					}
					addMessage("接收到IOT下发日志上传命令: " + logType);
					if (logType != LogUploader.LOG_TYPE_APP
						&& logType != LogUploader.LOG_TYPE_ECU
						&& logType != LogUploader.LOG_TYPE_RTK) {
						//不支持的日志类型
						HiveMqClient.INSTANCE.replyGetLog(platformDTO.getMessageId(), false);
						return;
					}
					startUploadLog(logType);
					HiveMqClient.INSTANCE.replyGetLog(platformDTO.getMessageId(), true);
					break;
				}
				case MqttConstants.FUNCTION_CONFIG_CHANGE: {
					int type = 0;
					for (InputsDTO input : inputs) {
						if (TextUtils.equals(input.getName(), "changeType")) {
							type = (int) ((double) input.getValue());
							break;
						}
					}
					addMessage("接收到IOT下发配置更新命令type = " + type);
					if (type == 1) {
						//大屏配置变更
						LargeScreenWebSocketManager.getInstance().sendData(new LargeScreenConfigChangeDTO(type));
					} else if (type == 2) {
						// 告警配置变更
						queryAlarmConfigList();
					} else if (type == 3) {
						// 塔吊属性配置变更
						queryTowerConfig();
					} else if (type == 4) {
						//系统配置变更
						querySystemConfig();
						LargeScreenWebSocketManager.getInstance().sendData(new LargeScreenConfigChangeDTO(type));
					} else if (type == 5) {
						//终端设备列表更新
					}
					HiveMqClient.INSTANCE.replyConfigChange(platformDTO.getMessageId());
					break;
				}
				case MqttConstants.FUNCTION_TOWER_CALIBRATION: {
					//塔身标定
					int command = 0;
					for (InputsDTO input : inputs) {
						if (TextUtils.equals(input.getName(), "command")) {
							command = (int) ((double) input.getValue());
							break;
						}
					}
					addMessage("接收到IOT下发塔身标定指令 = " + command);
					break;
				}
				default:
					break;
			}
		});
	}

	private void saveHistoryPath(List<HistoryPoint> historyPointList) {
		if (CsvUtil.writeCsvFile(MemoryStore.getInstance().getTaskId(), historyPointList)) {
			addMessage("A->B轨迹保存成功");
		} else {
			addMessage("A->B轨迹保存失败");
		}
		historyPointList.clear();
	}

	/**
	 * 更新ecu基本配置
	 */
	private void updateEcuTowerConfig(TowerConfig towerConfig) {
		ThreadExecutor.getInstance().executor(() -> {
			int towerCraneType = FileStore.getSystemConfig().getCraneType();
			addMessage("正在下发塔吊配置到ECU...");
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.configTowerType(towerCraneType));
			SystemClock.sleep(50);

			double armLength = 0;
			if (towerConfig != null
				&& towerConfig.getTowerBaseConfig() != null
				&& towerConfig.getTowerBaseConfig().getTowerArmLength() != null) {
				armLength = towerConfig.getTowerBaseConfig().getTowerArmLength();
			}
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setArm(armLength, 10, 0));
			SystemClock.sleep(50);

			GeoData towerPos = new GeoData(0, 0, 0);
			if (towerConfig != null
				&& towerConfig.getTowerBaseConfig() != null
				&& towerConfig.getTowerBaseConfig().getTowerPos() != null) {
				towerPos = towerConfig.getTowerBaseConfig().getTowerPos();
			}
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setLocation(ProtocolHelper.TYPE_C, towerPos.getLat(), towerPos.getLng(), towerPos.getAlt()));
			SystemClock.sleep(50);

//			double trolleyWidth = 0;
//			if (towerConfig != null && towerConfig.getTowerBaseConfig() != null && towerConfig.getTowerBaseConfig().getTrolleyWidth() != null) {
//				trolleyWidth = towerConfig.getTowerBaseConfig().getTrolleyWidth();
//			}
//			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setTrolleyWidth(trolleyWidth));
//			SystemClock.sleep(50);

			double inclinationOffset = 0;
			if (towerConfig != null
				&& towerConfig.getCalibration() != null) {
				inclinationOffset = towerConfig.getCalibration().getInclination();
			}
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setInclinationOffset(inclinationOffset));
			SystemClock.sleep(50);

			List<Pair<Double, Double>> table = new ArrayList<>();
			if (towerConfig != null && towerConfig.getLoadCapacityChart() != null) {
				List<LoadingConfig> loadTable = towerConfig.getLoadCapacityChart();
				for (LoadingConfig loadingConfig : loadTable) {
					Pair<Double, Double> pair = new Pair<>(loadingConfig.getRadius(), loadingConfig.getLimitWeight());
					table.add(pair);
				}
			}
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setLoadTable(table));
			SystemClock.sleep(50);

			SlowdownDelayConfig delayConfig = FileStore.getDelayConfig();
			if (delayConfig != null) {
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.configSlowdownDelay(
					delayConfig.getSpin1(), delayConfig.getSpin2(),
					delayConfig.getVariation1(), delayConfig.getVariation2(), delayConfig.getVariation3(), delayConfig.getVariation4(),
					delayConfig.getHook1(), delayConfig.getHook2(), delayConfig.getHook3(), delayConfig.getHook4()
				));
			}
//			SystemClock.sleep(50);
//
//			Logger.d(TAG, "设置半自动安全高度");
//			double up = FileStore.getLiftUpHeightConfig();
//			double down = FileStore.getLiftDownHeightConfig();
//			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setSemiAutoLiftingHeight(up, down));
		});
	}

	/**
	 * 更新ecu预警配置
	 */
	private void updateEcuAlarmConfig() {
		List<AlarmConfig> alarmConfigList = MemoryStore.getInstance().getAlarmConfigList();
		List<Object[]> configs = new ArrayList<>();
		if (alarmConfigList != null) {
			for (AlarmConfig config : alarmConfigList) {
				configs.add(new Object[]{config.getParamType(), config.getAlarmLvl() == AlarmConfig.ALARM_LEVEL_NORMAL ? 1 : 2, config.getComparatorType(), config.getAlarmValue()});
			}
		}
		TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setAlarmConfigList(configs));
	}

	private void handlePadMessage(String msgType, String msgId, String json) {
		if (msgType == null) {
			return;
		}
		ThreadExecutor.getInstance().executor(() -> {
			switch (msgType) {
				case WebSocketMessage.PATH_CONTROL: {
					WebSocketMessage<PathControlCommand> command = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<PathControlCommand>>() {
					}.getType());
					if (command == null || command.getData() == null) {
						Logger.e(TAG, "handlePadMessage pathControl parse error");
						return;
					}
					handlePathControl(command);
					break;
				}
				case WebSocketMessage.STATUS_CONTROL: {
					WebSocketMessage<StatusControlCommand> command = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<StatusControlCommand>>() {
					}.getType());
					if (command == null || command.getData() == null) {
						Logger.e(TAG, "handlePadMessage statusControl parse error");
						return;
					}
					handleStatusControl(command);
					break;
				}
				case WebSocketMessage.LOGIN_STATUS_UPDATE: {
					try {
						WebSocketMessage<LoginStatusUpdateDTO> command = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<LoginStatusUpdateDTO>>() {
						}.getType());
						if (command == null || command.getData() == null) {
							Logger.e(TAG, "handlePadMessage loginStatusUpdate parse error");
							return;
						}
						LoginStatusUpdateDTO loginInfo = command.getData();
						int status = loginInfo.getStatus();
						if (status != 1) {
							//大屏锁屏时将保存的uid置为null
							MemoryStore.getInstance().setUid(null);
						} else {
							MemoryStore.getInstance().setUid(loginInfo.getUid());
						}
						if (status != MemoryStore.getInstance().getLoginStatus()) {
							addMessage("接收到登录状态更新消息 -> " + status + ", uid: " + loginInfo.getUid());
							MemoryStore.getInstance().setLoginStatus(status);
						}
						TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateDriverLoginStatus(status == 1));
					} catch (Exception e) {
						//防止司机中控老版本发送数据不对导致解析异常崩溃
					}
					break;
				}
				case WebSocketMessage.CONFIG_SLOWDOWN_DELAY: {
					WebSocketMessage<SlowdownDelayConfig> command = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<SlowdownDelayConfig>>() {
					}.getType());
					if (command == null || command.getData() == null) {
						Logger.e(TAG, "handlePadMessage slowdownConfig parse error");
						broadcastToTowerPad(Messenger.replyDelayConfig(command == null ? "" : command.getMessageId(), false));
						return;
					}
					SlowdownDelayConfig config = command.getData();
					Logger.d(TAG, "接收到中控缓停配置消息 -> " + config);
					addMessage("接收到中控缓停配置消息");
					FileStore.setDelayConfig(config);
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.configSlowdownDelay(
						config.getSpin1(), config.getSpin2(),
						config.getVariation1(), config.getVariation2(), config.getVariation3(), config.getVariation4(),
						config.getHook1(), config.getHook2(), config.getHook3(), config.getHook4()
					));
					broadcastToTowerPad(Messenger.replyDelayConfig(command.getMessageId(), true));
					break;
				}
				case WebSocketMessage.START_AUTO_TASK: {
					WebSocketMessage<SemiautomaticTask> command = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<SemiautomaticTask>>() {
					}.getType());
					if (command == null || command.getData() == null) {
						Logger.e(TAG, "handlePadMessage: start task parse error");
						return;
					}
					handleSemiAutomaticTask(command.getData());
					break;
				}
				case WebSocketMessage.UPDATE_TASK_STATUS: {
					WebSocketMessage<Integer> command = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<Integer>>() {
					}.getType());
					if (command == null || command.getData() == null) {
						Logger.e(TAG, "handlePadMessage: resume task cmd parse error");
						return;
					}
					int cmd = command.getData();
					addMessage("接收到请求半自动吊运任务状态更新消息: " + cmd);
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.updateSemiautomaticTask(cmd));
					break;
				}
				case WebSocketMessage.COLLECT_POINT: {
					addMessage("接收到采点请求");
					handleCollectPoint(msgId);
					break;
				}
				case WebSocketMessage.START_PLANNED_PATH_TASK: {
					addMessage("接收到开始执行自动规划路径任务请求");
					startPlannedPathTask();
					break;
				}
				case WebSocketMessage.RESTART_CONTROL_APP:
					AppUtils.relaunchApp(true);
					break;
				default:
					break;
			}
		});
	}

	private void startPlannedPathTask() {
		Logger.d(TAG, "startPlannedPathTask");
		ThreadExecutor.getInstance().executor(() -> {
			File pathFile = new File(Environment.getExternalStorageDirectory() + File.separator + "path.json");
			if (!FileUtils.isFile(pathFile)) {
				addMessage("自动规划路径文件不存在");
				Logger.e(TAG, "startPlannedPathTask: path file not exist");
				return;
			}
			try {
				List<PathPoint> paths = GsonUtils.fromJson(new FileReader(pathFile), new TypeToken<List<PathPoint>>() {
				}.getType());
				if (CollectionUtils.isEmpty(paths)) {
					Logger.e(TAG, "startPlannedPathTask: path is empty");
					addMessage("自动规划路径为空");
					return;
				}
				List<HistoryPoint> historyPointList = paths.stream()
					.map(pathPoint -> {
						HistoryPoint historyPoint = new HistoryPoint(
							System.currentTimeMillis(),
							pathPoint.getLatitude(),
							pathPoint.getLongitude(),
							pathPoint.getAltitude()
						);
						switch (pathPoint.getAction()) {
							case "lift":
								//抬臂（lift）->物体回收
								historyPoint.setTrolleyGear(-1);
								break;
							case "press":
								//压臂(press)->物体外推
								historyPoint.setTrolleyGear(1);
								break;
							case "down":
								//放绳(down)->物体下降
								historyPoint.setHookGear(-1);
								break;
							case "up":
								//收绳(up)->物体上升
								historyPoint.setHookGear(1);
								break;
							case "left":
								//左旋(left)
								historyPoint.setSpinGear(-1);
								break;
							case "right":
								//右旋(right)
								historyPoint.setSpinGear(1);
								break;
						}
						return historyPoint;
					})
					.collect(Collectors.toList());
				addMessage("开始下发规划路径");
				TaskHistorySender.getInstance().start(historyPointList, new TaskHistorySender.Callback() {
					@Override
					public void onSuccess() {
						// 开始任务，控制吊钩到A
						Logger.d(TAG, "startPlannedPathTask: send path finished");
						addMessage("规划路径下发成功，开始自动吊运");
						TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_B));
					}

					@Override
					public void onError(int code, String msg) {
						Logger.e(TAG, "startPlannedPathTask onError: code -> " + code + ", msg -> " + msg);
						addMessage("规划路径下发失败: " + msg);
					}
				});

			} catch (FileNotFoundException e) {
				Logger.e(TAG, "startPlannedPathTask exception: " + e.getMessage());
			}
		});
	}

	public void handleSemiAutomaticTask(SemiautomaticTask task) {
		double upHeight = FileStore.getLiftUpHeightConfig();
		double downHeight = FileStore.getLiftDownHeightConfig();
		Logger.d(TAG, "handleSemiAutomaticTask: upHeight -> " + upHeight + ", downHeight -> " + downHeight + ", target -> " + task.getHook());
		addMessage("开始半自动吊运，安全上升高度：" + upHeight + ", 下降高度：" + downHeight + ", 目标点：" + task.getHook());
		TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.startSemiAutomaticTask(upHeight, downHeight, task.getHook(), task.getTower(), task.getHeading(), task.getJibAngle()));
	}

	/**
	 * 处理地面显控发送的任务采集命令
	 *
	 * @param command 任务采集命令
	 */
	private void handlePathControl(WebSocketMessage<PathControlCommand> command) {
		PathControlCommand commandData = command.getData();
		int pathControlCommand = Integer.parseInt(commandData.getCommand());
		if (pathControlCommand == PathControlCommand.COMMAND_CANCEL_COLLECT) {
			// 接收到取消采集命令，取消当前路径采集任务
			MemoryStore.getInstance().setPathControlCommand(pathControlCommand);
//			historyPointList.clear();
			broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.SUCCESS));
			return;
		}
		EcuRealtimeData ecuRealtimeStatus = ecuRealtimeDataLd.getValue();
		if (ecuRealtimeStatus == null) {
			broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.ECU_STATUS_ERROR));
			return;
		}
		if (ecuRealtimeStatus.getCraneStatus() != 3 || ecuRealtimeStatus.getModeParent() != 2) {
			// 当前工作状态不是：远程手动模式+工作状态,不可以执行任务采集
			broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.TOWER_STATUS_NOT_WAITING));
			return;
		}
		addMessage("接收到地面显控下发的任务采集命令 -> " + pathControlCommand);
		Logger.d(TAG, "handlePadMessage received PATH_CONTROL command: " + commandData);
		if (pathControlCommand == PathControlCommand.COMMAND_COLLECT_A || pathControlCommand == PathControlCommand.COMMAND_COLLECT_B) {
			EcuLog ecuLog = ecuLogLiveData.getValue();
			if (ecuLog == null || ecuLog.getHookGpsStatus() != 4) {
				broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.RTK_STATUS_ERROR));
				return;
			}
		}
		MemoryStore.getInstance().setTaskId(commandData.getTaskId());
		MemoryStore.getInstance().setPathControlCommand(pathControlCommand);
		if (pathControlCommand == PathControlCommand.COMMAND_FINISH_COLLECT) {
			// 保存A->B历史轨迹
//			saveHistoryPath(historyPointList);
		}
		broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.SUCCESS));
	}

	/**
	 * 处理地面显控发送的任务执行命令
	 *
	 * @param command 任务执行命令
	 */
	private void handleStatusControl(WebSocketMessage<StatusControlCommand> command) {
		StatusControlCommand commandData = command.getData();
		int statusControlCommand = Integer.parseInt(commandData.getCommand());
		int mode = commandData.getMode();
		addMessage("接收到地面显控下发的任务执行命令 -> " + statusControlCommand);
		Logger.d(TAG, "handlePadMessage received STATUS_CONTROL command: " + commandData);
		MemoryStore.getInstance().setTaskId(commandData.getTaskId());
		switch (statusControlCommand) {
			case StatusControlCommand.COMMAND_START_TASK:
				// 开始吊运任务
				EcuRealtimeData ecuRealtimeStatus = ecuRealtimeDataLd.getValue();
				if (ecuRealtimeStatus == null) {
					broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.ECU_STATUS_ERROR));
					return;
				}
				if (ecuRealtimeStatus.getModeParent() != 3 || ecuRealtimeStatus.getCraneStatus() != 2) {
					// 当前塔吊非自动托管空闲状态,无法开始任务
					broadcastToTowerPad(Messenger.replyPathControl(command.getMessageId(), ErrorCode.TOWER_START_TASK_FAILED));
					return;
				}
				if (mode == StatusControlCommand.MODE_RECOMMEND_PATH) {
					// 开始推荐路径模式
					startRecommendPathTask(command.getMessageId(), commandData.getTaskId());
				} else if (mode == StatusControlCommand.MODE_SMART_PLANNING) {
					// 开始智能规划模式到A点
					startSmartPlanningTask(command.getMessageId(), commandData.getPointA(), commandData.getPointB(), ProtocolHelper.WORKING_MODE_A);
				}
				break;
			case StatusControlCommand.COMMAND_FINISH_A:
				// 装载完成，开始A->B
				if (mode == StatusControlCommand.MODE_RECOMMEND_PATH) {
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_B));
				} else if (mode == StatusControlCommand.MODE_SMART_PLANNING) {
					TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_SMART_PLANNING_B));
				}
				broadcastToTowerPad(Messenger.replyStatusControl(command.getMessageId(), ErrorCode.SUCCESS));
				break;
			case StatusControlCommand.COMMAND_FINISH_B:
				// 卸载完成,开始让吊钩升到最高点
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_UP));
				broadcastToTowerPad(Messenger.replyStatusControl(command.getMessageId(), ErrorCode.SUCCESS));
				break;
			case StatusControlCommand.COMMAND_CANCEL_TASK:
				// 取消吊运任务
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_FREE));
				broadcastToTowerPad(Messenger.replyStatusControl(command.getMessageId(), ErrorCode.SUCCESS));
				break;
			case StatusControlCommand.COMMAND_PAUSE_TASK:
				// 暂停吊运任务
				EcuLog ecuLog = ecuLogLiveData.getValue();
				if (ecuLog != null) {
					pausedHookPosition = ecuLog.getHookLocation();
				}
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_FREE));
				broadcastToTowerPad(Messenger.replyStatusControl(command.getMessageId(), ErrorCode.SUCCESS));
				break;
			case StatusControlCommand.COMMAND_RESUME_TASK:
				// 继续吊运任务
				startSmartPlanningTask(command.getMessageId(), pausedHookPosition, commandData.getPointB(), ProtocolHelper.WORKING_MODE_SMART_PLANNING_B);
				break;
			default:
				break;
		}
	}

	private void handleRetracingTask(int type) {
		Logger.d(TAG, "handleSemiAutomaticLifting: " + type);
		String name = (type == 1 ? "A_to_B" : "B_to_A");
		if (!CsvUtil.fileExist(name)) {
			addMessage("轨迹未采集！");
			return;
		}
		EcuRealtimeData ecuRealtimeData = this.ecuRealtimeDataLd.getValue();
		EcuLog ecuLog = ecuLogLiveData.getValue();
		if (ecuRealtimeData == null || ecuLog == null) {
			Logger.e(TAG, "handleSemiAutomaticLifting: ecu data error");
			return;
		}
		if (ecuRealtimeData.getModeParent() != 2) {
			addMessage("当前非遥控模式，无法进行半自动吊运");
			Logger.e(TAG, "handleSemiAutomaticLifting: ecu mode error");
			return;
		}
		if (ecuRealtimeData.getHookGear() != 0
			|| ecuRealtimeData.getSlewGear() != 0
			|| ecuRealtimeData.getTrolleyGear() != 0) {
			addMessage("当前非全空档，无法进行半自动吊运");
			Logger.e(TAG, "handleSemiAutomaticLifting: ecu mode error");
			return;
		}
		List<HistoryPoint> historyPointList = CsvUtil.readCsvFile(name);
		if (CollectionUtils.isEmpty(historyPointList)) {
			addMessage("无轨迹记录");
			Logger.e(TAG, "handleSemiAutomaticLifting: history record is empty");
			return;
		}
		HistoryPoint startPoint = historyPointList.get(0);
		GeoData currPosition = ecuLog.getHookLocation();
		double distance = Calculator.calculate3DDistance(startPoint.getLat(), startPoint.getLng(), startPoint.getAlt(), currPosition.getLat(), currPosition.getLng(), currPosition.getAlt());
		if (distance > 2) {
			Logger.e(TAG, "handleSemiAutomaticLifting: distance is too far -> " + distance);
			addMessage(StringUtils.format("距离起吊点%.2f m,无法开始半自动吊运", distance));
			return;
		}
		addMessage("轨迹下发中...");
		TaskHistorySender.getInstance().start(historyPointList, new TaskHistorySender.Callback() {
			@Override
			public void onSuccess() {
				// 开始任务，控制吊钩到A
				Logger.d(TAG, "handleSemiAutomaticLifting: send history finished");
				addMessage("轨迹发送成功,开始半自动吊运");
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_B));
			}

			@Override
			public void onError(int code, String msg) {
				Logger.e(TAG, "TaskHistorySender onError: code -> " + code + ", msg -> " + msg);
				addMessage("轨迹发送失败: " + msg);
			}
		});
	}

	private void startRecommendPathTask(String messageId, String taskId) {
		if (!CsvUtil.fileExist(taskId)) {
			broadcastToTowerPad(Messenger.replyStatusControl(messageId, ErrorCode.TOWER_TASK_POINTS_SEND_FAILED));
			return;
		}

		// 发送路径
		List<HistoryPoint> historyPointList = CsvUtil.readCsvFile(MemoryStore.getInstance().getTaskId());
		TaskHistorySender.getInstance().start(historyPointList, new TaskHistorySender.Callback() {
			@Override
			public void onSuccess() {
				// 开始任务，控制吊钩到A
				addMessage("轨迹发送成功");
				TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_A));
				broadcastToTowerPad(Messenger.replyStatusControl(messageId, ErrorCode.SUCCESS));
			}

			@Override
			public void onError(int code, String msg) {
				Logger.e(TAG, "TaskHistorySender onError: code -> " + code + ", msg -> " + msg);
				addMessage("轨迹发送失败");
				broadcastToTowerPad(Messenger.replyStatusControl(messageId, ErrorCode.TOWER_TASK_POINTS_SEND_FAILED));
			}
		});
	}

	private void startSmartPlanningTask(String messageId, GeoData pointA, GeoData pointB, int workingMode) {
		if (pointA != null) {
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setLocation(ProtocolHelper.TYPE_A, pointA.getLat(), pointA.getLng(), pointA.getAlt() + 3.5));
			SystemClock.sleep(50);
		} else {
			Logger.e(TAG, "startSmartPlanningTask: pointA is null");
		}
		if (pointB != null) {
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setLocation(ProtocolHelper.TYPE_B, pointB.getLat(), pointB.getLng(), pointB.getAlt() + 3.5));
			SystemClock.sleep(50);
		} else {
			Logger.e(TAG, "startSmartPlanningTask: pointB is null");
		}
		TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(workingMode));
		broadcastToTowerPad(Messenger.replyStatusControl(messageId, ErrorCode.SUCCESS));
	}

	private void runOnUiThread(Runnable r) {
		mainHandler.post(r);
	}

	/**
	 * 向塔吊触屏端发送消息
	 *
	 * @param msg 要发送的消息
	 */
	private <T> void broadcastToTowerPad(WebSocketMessage<T> msg) {
		String json = GsonUtils.toJson(msg);
//		Logger.d(TAG, "broadcastToTowerPad: " + json);
		TowerApp.getWebSocketServer().broadcast(json);
	}

	private void addMessage(String msg) {
		runOnUiThread(() -> {
			if (logQueue.size() > 200) {
				logQueue.poll();
			}
			logQueue.offer(new ScreenLog(TimeUtils.date2String(new Date()), msg));
			screenLogLiveData.setValue(logQueue);
		});
	}

	/**
	 * IOT平台注册设备
	 */
	public void registerDevice() {
		RepositoryContainer.getInstance()
			.getDeviceRepository()
			.registerDevice(new com.fj.towercontrol.data.net.callback.Callback<>() {
				@Override
				public void onSuccess(RegisterDeviceResp registerDeviceResp) {
					addMessage("设备注册成功");
				}

				@Override
				public void onError(String code, String msg) {
					if (TextUtils.equals(code, "duplicate_key")) {
						//设备已经注册过
						return;
					}
					addMessage("设备注册失败, code: " + code + ", msg: " + msg);
				}
			});
	}

	private void startUploadLog(int logType) {
		LogUploader.getInstance()
			.start(logType, new LogUploader.LogUploadCallback() {
				@Override
				public void onProgress(String msg) {
					addMessage(msg);
					Logger.d(TAG, msg);
				}

				@Override
				public void onFinish() {
					addMessage("日志上传结束");
					Logger.d(TAG, "日志上传结束");
				}

				@Override
				public void onError(int code, String msg) {
					addMessage("日志上传异常: code -> " + code + ", msg -> " + msg);
					Logger.e(TAG, "日志上传异常: code -> " + code + ", msg -> " + msg);
				}
			});
	}

	/**
	 * 监听Mqtt连接状态变化
	 *
	 * @param connected 连接是否成功
	 */
	public void handleMqttStatusChanged(boolean connected) {
		if (mqttConnected != null && mqttConnected == connected) {
			return;
		}
		Logger.d(TAG, "handleMqttStatusChanged: " + connected);
		mqttConnected = connected;
		if (connected) {
			addMessage("已连接到IOT平台");
		} else {
			addMessage("已断开IOT平台连接");
		}
	}

	/**
	 * 开始检查升级
	 */
	public void startCheckUpgrade() {
		OtaManager.getInstance().startCheckUpgrade(this::addMessage);
	}

	public void handleCollectPoint(String msgId) {
		EcuRealtimeData status = ecuRealtimeDataLd.getValue();
		EcuLog ecuLog = ecuLogLiveData.getValue();
		if (status == null
			|| ecuLog == null) {
			addMessage("点采集失败：ecu数据异常");
			broadcastToTowerPad(Messenger.replyCollectPoint(msgId, ErrorCode.ECU_STATUS_ERROR, null));
			return;
		}
		if (ecuLog.getHookGpsStatus() != 4) {
			addMessage("点采集失败：吊钩非固定解");
			broadcastToTowerPad(Messenger.replyCollectPoint(msgId, ErrorCode.RTK_STATUS_ERROR, null));
			return;
		}
		GeoData hook = ecuLog.getHookLocation();
		GeoData tower = ecuLog.getTowerLocation();
//		FileStore.setTestPoint(hook);
		SemiautomaticTask task = new SemiautomaticTask(hook, tower, status.getYaw(), status.getLuffingAngle());
		broadcastToTowerPad(Messenger.replyCollectPoint(msgId, ErrorCode.SUCCESS, task));
		addMessage("目标点已采集：" + hook + ", r: " + status.getWorkRadius());
	}

	public void stop() {
		TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_FREE));
	}

//	public void testStopTask() {
//		TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.setWorkingMode(ProtocolHelper.WORKING_MODE_FREE));
//	}
//
//	public void testCollect() {
//		EcuRealtimeData status = ecuRealtimeDataLd.getValue();
//		EcuLog ecuLog = ecuLogLiveData.getValue();
//		if (status == null
//			|| ecuLog == null
//			|| ecuLog.getHookLocation() == null) {
//			addMessage("点采集失败：ecu数据异常");
//			return;
//		}
//		if (ecuLog.getHookGpsStatus() != 4) {
//			addMessage("点采集失败：吊钩非固定解");
//			return;
//		}
//		GeoData hook = ecuLog.getHookLocation();
//		GeoData tower = ecuLog.getTowerLocation();
//		SemiautomaticTask task = new SemiautomaticTask(hook, tower, status.getYaw(), status.getLuffingAngle());
//		MemoryStore.getInstance().setSemiautomaticTask(task);
//		addMessage("目标点已采集：" + hook + ", r: " + status.getWorkRadius());
//	}
//
//	public void testStartTask() {
//		SemiautomaticTask task = MemoryStore.getInstance().getSemiautomaticTask();
//		if (task == null) {
//			addMessage("任务开始失败：请先采集目标点");
//			return;
//		}
//		handleSemiAutomaticTask(task);
//	}

	private static final class EcuHeartbeatTask extends TimerTask {
		public void run() {
			TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.heartbeat());
		}
	}
}
