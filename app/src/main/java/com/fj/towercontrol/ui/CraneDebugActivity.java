package com.fj.towercontrol.ui;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;

import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.fj.towercontrol.R;
import com.fj.towercontrol.consts.CraneType;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.EcuDataParser;
import com.fjdynamics.app.logger.Logger;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * 不同类型塔吊数据调试页
 *
 * <AUTHOR>
 */
public class CraneDebugActivity extends AppCompatActivity {
	private static final String TAG = "CraneDebugActivity";
	public static final String KEY_CRANE_TYPE = "CRANE_TYPE";
	private DebugViewModel viewModel;
	private TextView tvStatus;
	private FrameLayout backButton;

	@Override
	protected void onCreate(@Nullable Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		// 设置全屏隐藏导航栏
		Window window = getWindow();
		window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
		window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
		window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
		window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
		window.setStatusBarColor(Color.TRANSPARENT);
		WindowManager.LayoutParams attributes = window.getAttributes();
		attributes.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE;
		window.setAttributes(attributes);

		setContentView(R.layout.activity_sany);
		Logger.d(TAG, "onCreate");
		viewModel = new ViewModelProvider(this).get(DebugViewModel.class);
		initView();
		initData();
		EventBus.getDefault().register(this);
	}

	private void initView() {
		backButton = findViewById(R.id.fl_back);
		tvStatus = findViewById(R.id.tv_status);
	}

	private void initData() {
		backButton.setOnClickListener(v -> finish());
		Intent intent = getIntent();
		if (intent == null) {
			ToastUtils.showShort("塔吊类型异常");
			finish();
			return;
		}
		CraneType craneType = (CraneType) intent.getSerializableExtra(KEY_CRANE_TYPE);
		TextView tvTitle = findViewById(R.id.tv_title);
		if (craneType == CraneType.SANY) {
			tvTitle.setText("三一塔吊");
			viewModel.getEcuSanyStatus().observe(this, sany -> {
				if (sany == null) {
					return;
				}
				tvStatus.setText(StringUtils.format(
					"""
						normal_torque：%s，serial_num：%s，
						sys_work_time：%s，power_work_time：%s，
						armLength：%s，work_mode：%s，
						rise_gear：%s，rotate_gear：%s，luff_gear：%s
						wind_speed：%s，倍率：%s，
						amplitude：%s，height：%s，angle：%s
						rated_load：%s，real_load：%s，tq_percent：%s
						upest：%s，up2slow：%s，down2slow：%s，downest：%s，
						nearest：%s，near2slow：%s，far2slow：%s，farest：%s，
						leftest：%s，left2slow：%s，rightest：%s，right2slow：%s，
						weight_waring：%s，weight_alarm：%s，tq_waring：%s，tq_alarm：%s，
						wind_waring：%s，wind_alarm：%s，rise_inverter_err：%s，rotate_inverter_err：%s，
						luff_inverter_err：%s，phase_warning：%s，no_power：%s，bypath_swith：%s，
						rise_inverter_err_code：%s，rotate_inverter_err_code：%s，luff_inverter_err_code：%s，
						is_power_on：%s，sys_fault：%s，is_sys_reset：%s，sys_reset_button：%s，
						tq_80percent：%s，tq_110percent：%s，weight_80percent：%s，weight_110percent：%s，
						back_up_1：%s，elect_power：%s，back_up_2：%s，elect_power_botton：%s，
						buzzer：%s，
						塔机远控状态：%s，远程急停状态：%s，塔机急停状态：%s，塔机心跳计数：%s，
						变幅挡位：%s,%s,%s，回转挡位：%s,%s,%s，起升挡位：%s,%s,%s，ECU故障复位指令：%s，
						ECU回转抱闸指令：%s，ECU动力电源指令：%s，ECU急停指令：%s，ECU请求登录：%s"""
					,
					sany.getNominalTorque(), sany.getSerialNum(),
					sany.getSysWorkTime(), sany.getPowerWorkTime(),
					sany.getArmLength(), sany.getWorkMode(),
					sany.getRiseGear(), sany.getRotateGear(), sany.getLuffGear(),
					sany.getWindSpeed(), sany.getMultiplier(),
					sany.getAmplitude(), sany.getHeight(), sany.getAngle(),
					sany.getRatedLoad(), sany.getRealLoad(), sany.getTqPercent(),
					EcuDataParser.hasBitN(sany.getUpset(), 0), EcuDataParser.hasBitN(sany.getUpset(), 1), EcuDataParser.hasBitN(sany.getUpset(), 2), EcuDataParser.hasBitN(sany.getUpset(), 3),
					EcuDataParser.hasBitN(sany.getUpset(), 4), EcuDataParser.hasBitN(sany.getUpset(), 5), EcuDataParser.hasBitN(sany.getUpset(), 6), EcuDataParser.hasBitN(sany.getUpset(), 7),
					EcuDataParser.hasBitN(sany.getLeftset(), 0), EcuDataParser.hasBitN(sany.getLeftset(), 1), EcuDataParser.hasBitN(sany.getLeftset(), 2), EcuDataParser.hasBitN(sany.getLeftset(), 3),
					EcuDataParser.hasBitN(sany.getLeftset(), 4), EcuDataParser.hasBitN(sany.getLeftset(), 5), EcuDataParser.hasBitN(sany.getLeftset(), 6), EcuDataParser.hasBitN(sany.getLeftset(), 7),
					EcuDataParser.hasBitN(sany.getWindWarning(), 0), EcuDataParser.hasBitN(sany.getWindWarning(), 1), EcuDataParser.hasBitN(sany.getWindWarning(), 2), EcuDataParser.hasBitN(sany.getWindWarning(), 3),
					EcuDataParser.hasBitN(sany.getWindWarning(), 4), EcuDataParser.hasBitN(sany.getWindWarning(), 5), EcuDataParser.hasBitN(sany.getWindWarning(), 6), EcuDataParser.hasBitN(sany.getWindWarning(), 7),
					sany.getRiseInverterErrorCode(), sany.getRotateInverterErrorCode(), sany.getLuffInverterErrorCode(),
					EcuDataParser.hasBitN(sany.getPowerOn(), 0), EcuDataParser.hasBitN(sany.getPowerOn(), 1), EcuDataParser.hasBitN(sany.getPowerOn(), 2), EcuDataParser.hasBitN(sany.getPowerOn(), 3),
					EcuDataParser.hasBitN(sany.getTq80Percent(), 0), EcuDataParser.hasBitN(sany.getTq80Percent(), 1), EcuDataParser.hasBitN(sany.getTq80Percent(), 2), EcuDataParser.hasBitN(sany.getTq80Percent(), 3),
					EcuDataParser.hasBitN(sany.getTq80Percent(), 4), EcuDataParser.hasBitN(sany.getTq80Percent(), 5), EcuDataParser.hasBitN(sany.getTq80Percent(), 6), EcuDataParser.hasBitN(sany.getTq80Percent(), 7),
					EcuDataParser.hasBitN(sany.getBuzzer(), 0),
					sany.getTowerRemoteStatus() == 0 ? "近控" : "远控", sany.getRemoteStopStatus(), sany.getTowerStopStatus(), sany.getTowerHeartbeatCount(),
					sany.getEcuVariationGear(), sany.getEcuOutputVariationGear(), sany.getEcuInputVariationGear(),
					sany.getEcuSpinGear(), sany.getEcuOutputSpinGear(), sany.getEcuInputSpinGear(),
					sany.getEcuLiftGear(), sany.getEcuOutputLiftGear(), sany.getEcuInputLiftGear(),
					sany.getEcuRecoveryCommand(),
					sany.getEcuSpinCommand(), sany.getEcuPowerCommand(), sany.getEcuStopCommand(), sany.getEcuRequestLogin()
				));
			});
		} else if (craneType == CraneType.XCMG) {
			tvTitle.setText("徐工塔吊");
			viewModel.getEcuXCMGData().observe(this, data -> {
				if (data == null) {
					return;
				}
				tvStatus.setText(StringUtils.format(
					"""
						额定重量: %s, 实际重量: %s, 力矩百分比: %s, 变幅幅度: %s,\s
						吊臂长度: %s, 吊臂角度: %s, 吊钩高度: %s, 风速: %s,\s
						回转角度: %s, 倍率: %s,\s
						E故障码: %s, EW故障码: %s, EC故障码: %s, ES故障码: %s,\s
						起升变频器故障码: %s, 变幅变频器故障码: %s, 回转变频器故障码: %s,\s
						塔机控制状态: %s, 回转状态: %s, 动力电源状态: %s,\s
						座舱起升挡位: %s, 变幅挡位: %s, 回转挡位: %s,\s
						ECU起升挡位: %s, 变幅挡位: %s, 回转挡位: %s,\s
						塔机起升挡位: %s, 变幅挡位: %s, 回转挡位: %s,\s
						起升变幅器故障: %s, 变幅变幅器故障: %s, 回转变幅器故障: %s,\s
						塔上急停状态: %s, 座舱急停状态: %s, 风标状态: %s,\s
						蜗速状态: %s""",
					data.getRatedWeight(), data.getActualWeight(), data.getTorquePercent(), data.getAmplitude(),
					data.getJibLength(), data.getJibAngle(), data.getHookHeight(), data.getWindSpeed(),
					data.getRotateAngle(), data.getMultiplicationFactor(),
					data.getEFaultCode(), data.getEwFaultCode(), data.getEcFaultCode(), data.getEsFaultCode(),
					data.getLiftInverterFaultCode(), data.getSlewInverterFaultCode(), data.getRotateInverterFaultCode(),
					data.getControlState(), data.getRotateState(), data.getPowerState(),
					data.getCockpitLiftGear(), data.getCockpitSlewGear(), data.getCockpitRotateGear(),
					data.getEcuLiftGear(), data.getEcuSlewGear(), data.getEcuRotateGear(),
					data.getCraneLiftGear(), data.getCranSlewGear(), data.getCraneRotateGear(),
					data.getLiftAmplitudeError(), data.getSlewAmplitudeError(), data.getRotateAmplitudeError(),
					data.getTowerEmergencyStop(), data.getCockpitEmergencyStop(), data.getWeatherVine(),
					data.getWormSpeed()
				));
			});
		} else if (craneType == CraneType.YONGMAO_STT153) {
			tvTitle.setText("永茂STT153");
			viewModel.getYongMaoData().observe(this, data -> {
				if (data == null) {
					return;
				}
				tvStatus.setText(StringUtils.format(
					"""
						回转角度: %s, 小车幅度: %s, 动臂俯仰角: %s,
						吊钩高度: %s, 吊钩起重量: %s, 起重力矩百分比: %s,
						风速: %s, 大臂长度: %s,
						转角传感器: %s, 高度传感器: %s, 幅度传感器: %s, 重量传感器值: %s,
						转角标定系数: %s, 高度标定系数: %s, 幅度标定系数: %s,
						重量标定系数一: %s, 重量标定系数二: %s, 重量标定系数三: %s,
						吊钩倍率: %s, 状态位A: %s,
						座舱起升挡位: %s, 座舱变幅挡位: %s, 座舱回转挡位: %s,
						ECU起升挡位: %s, ECU变幅挡位: %s, ECU回转挡位: %s,
						塔机起升挡位: %s, 塔机变幅挡位: %s, 塔机回转挡位: %s,
						""",
					data.getRotateAngle(), data.getTrolleyAmplitude(), data.getBoomPitchAngle(),
					data.getHookHeight(), data.getHookWeight(), data.getLiftingMomentPercent(),
					data.getWindSpeed(), data.getBoomLength(),
					data.getAngleSensor(), data.getHeightSensor(), data.getAmplitudeSensor(), data.getWeightSensor(),
					data.getAngleCalibrationFactor(), data.getHeightCalibrationFactor(), data.getAmplitudeCalibrationFactor(),
					data.getWeightCalibrationFactor1(), data.getWeightCalibrationFactor2(), data.getWeightCalibrationFactor3(),
					data.getHookMultiple(), data.getStatusA(),
					data.getCabLiftGear(), data.getCabAmplitudeGear(), data.getCabRotateGear(),
					data.getEcuLiftGear(), data.getEcuAmplitudeGear(), data.getEcuRotateGear(),
					data.getCraneLiftGear(), data.getCraneAmplitudeGear(), data.getCraneRotateGear()
				));
			});
		}
	}

	@Override
	protected void onDestroy() {
		EventBus.getDefault().unregister(this);
		super.onDestroy();
		Logger.d(TAG, "onDestroy");
	}

	@Subscribe(threadMode = ThreadMode.MAIN)
	public void onMessageEvent(MessageEvent event) {
		if (event == null || event.getCode() == null) {
			Logger.e(TAG, "onMessageEvent: event or code is null");
			return;
		}
		viewModel.handleMessageEvent(event);
	}

}
