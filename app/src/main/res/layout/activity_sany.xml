<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	android:layout_width="match_parent"
	android:layout_height="match_parent">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/layout_header"
		android:layout_width="match_parent"
		android:layout_height="57dp"
		android:background="@color/white"
		app:layout_constraintTop_toTopOf="parent">

		<FrameLayout
			android:id="@+id/fl_back"
			android:layout_width="wrap_content"
			android:layout_height="match_parent"
			android:paddingStart="21dp"
			android:paddingEnd="4dp"
			app:layout_constraintStart_toStartOf="parent">

			<ImageView
				android:layout_width="32dp"
				android:layout_height="32dp"
				android:layout_gravity="center"
				android:src="@drawable/ic_back_black" />
		</FrameLayout>

		<TextView
			android:id="@+id/tv_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:singleLine="true"
			android:textColor="#191923"
			android:textSize="24sp"
			android:textStyle="bold"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<TextView
		android:id="@+id/tv_status"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:layout_marginTop="12dp"
		android:paddingStart="180dp"
		android:paddingEnd="0dp"
		android:textColor="#000000"
		android:textSize="14sp"
		app:layout_constraintTop_toBottomOf="@+id/layout_header"
		app:layout_constraintVertical_chainStyle="packed" />
</androidx.constraintlayout.widget.ConstraintLayout>
