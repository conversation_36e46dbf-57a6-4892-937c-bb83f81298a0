<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:fadeScrollbars="false"
	android:scrollbars="vertical"
	tools:showIn="@layout/layout_main">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:layout_width="match_parent"
		android:layout_height="match_parent">

		<TextView
			android:id="@+id/tv_battery_info"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="6dp"
			android:layout_marginEnd="6dp"
			android:textColor="@color/black"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:text="吊钩电池:87.3%,状态:充电中" />

		<TextView
			android:id="@+id/tvEcuRealtimeTitle"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="6dp"
			android:layout_marginTop="8dp"
			android:text="ecu实时状态上报"
			android:textColor="#000000"
			android:textSize="18sp"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/tvEcuRealtime"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textColor="#000000"
			android:textSize="12sp"
			app:layout_constraintStart_toStartOf="@+id/tvEcuRealtimeTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuRealtimeTitle" />

		<TextView
			android:id="@+id/tvEcuLogTitle"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="8dp"
			android:text="ecu日志上报"
			android:textColor="#000000"
			android:textSize="18sp"
			app:layout_constraintStart_toStartOf="@+id/tvEcuRealtimeTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuRealtime" />

		<TextView
			android:id="@+id/tvEcuLog"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textColor="#000000"
			android:textSize="12sp"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuLogTitle" />

		<TextView
			android:id="@+id/tvCargoData"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/dimen_8dp"
			android:text="智能吊钩称重："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvEcuLog" />

		<!--		<TextView-->
		<!--			android:id="@+id/tvTower"-->
		<!--			android:layout_width="wrap_content"-->
		<!--			android:layout_height="wrap_content"-->
		<!--			android:layout_marginTop="@dimen/dimen_8dp"-->
		<!--			android:text="塔机："-->
		<!--			android:textColor="#000000"-->
		<!--			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"-->
		<!--			app:layout_constraintTop_toBottomOf="@+id/tvRadarBottom" />-->

		<TextView
			android:id="@+id/tvLidar"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginVertical="8dp"
			android:text="激光雷达："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvCargoData" />

		<TextView
			android:id="@+id/tvLightning"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginVertical="8dp"
			android:text="雷电预警："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvLidar" />

		<TextView
			android:id="@+id/tvRope"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginVertical="8dp"
			android:text="缆绳检测："
			android:textColor="#000000"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvLightning" />

		<Button
			android:id="@+id/btn_play_voice_1"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="播放1"
			android:visibility="visible"
			app:layout_constraintStart_toStartOf="@+id/tvEcuLogTitle"
			app:layout_constraintTop_toBottomOf="@+id/tvRope"
			tools:visibility="visible" />

		<Button
			android:id="@+id/btn_play_voice_2"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="4dp"
			android:text="播放2"
			android:visibility="visible"
			app:layout_constraintBottom_toBottomOf="@+id/btn_play_voice_1"
			app:layout_constraintStart_toEndOf="@+id/btn_play_voice_1"
			app:layout_constraintTop_toTopOf="@+id/btn_play_voice_1"
			tools:visibility="visible" />

		<Button
			android:id="@+id/btn_auto_lift_config"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="4dp"
			android:text="自动吊运配置"
			app:layout_constraintBottom_toBottomOf="@+id/btn_play_voice_1"
			app:layout_constraintStart_toEndOf="@+id/btn_play_voice_2"
			app:layout_constraintTop_toTopOf="@+id/btn_play_voice_1" />

	</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
