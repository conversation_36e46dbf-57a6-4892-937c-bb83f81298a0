package com.fj.towercontrol.util;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * 数学工具类测试
 *
 * <AUTHOR>
 */
public class MathUtilsTest {

    @Test
    public void testBasicArithmetic() {
        // Test basic arithmetic operations
        assertEquals("Addition should work", 5, 2 + 3);
        assertEquals("Subtraction should work", 1, 3 - 2);
        assertEquals("Multiplication should work", 6, 2 * 3);
        assertEquals("Division should work", 2.0, 6.0 / 3.0, 0.001);
    }

    @Test
    public void testDoubleComparison() {
        // Test double comparison with delta
        double value1 = 85.123456789;
        double value2 = 85.123456788;

        assertEquals("Double values should be approximately equal",
            value1, value2, 0.000000002);

        // Test that they are not exactly equal (using direct comparison)
        assertTrue("Double values should not be exactly equal", value1 != value2);
    }

    @Test
    public void testPercentageCalculation() {
        // Test percentage calculations similar to battery percentage
        short rawValue = 800;
        double percentage = rawValue * 1.0 / 10; // 80.0%

        assertEquals("Percentage calculation should be correct", 80.0, percentage, 0.001);

        // Test edge cases
        assertEquals("Zero percentage", 0.0, 0 * 1.0 / 10, 0.001);
        assertEquals("Max percentage", 100.0, 1000 * 1.0 / 10, 0.001);
    }

    @Test
    public void testRounding() {
        // Test rounding operations
        double value = 85.6789;

        // Round to 2 decimal places
        double rounded = Math.round(value * 100.0) / 100.0;
        assertEquals("Rounding to 2 decimal places", 85.68, rounded, 0.001);

        // Round to 1 decimal place
        double rounded1 = Math.round(value * 10.0) / 10.0;
        assertEquals("Rounding to 1 decimal place", 85.7, rounded1, 0.001);
    }

    @Test
    public void testMinMax() {
        // Test min/max operations
        assertEquals("Min of two values", 5, Math.min(5, 10));
        assertEquals("Max of two values", 10, Math.max(5, 10));

        // Test with doubles
        assertEquals("Min of two doubles", 5.5, Math.min(5.5, 10.5), 0.001);
        assertEquals("Max of two doubles", 10.5, Math.max(5.5, 10.5), 0.001);
    }

    @Test
    public void testAbsoluteValue() {
        // Test absolute value
        assertEquals("Absolute value of positive", 5, Math.abs(5));
        assertEquals("Absolute value of negative", 5, Math.abs(-5));
        assertEquals("Absolute value of zero", 0, Math.abs(0));

        // Test with doubles
        assertEquals("Absolute value of positive double", 5.5, Math.abs(5.5), 0.001);
        assertEquals("Absolute value of negative double", 5.5, Math.abs(-5.5), 0.001);
    }

    @Test
    public void testSquareRoot() {
        // Test square root
        assertEquals("Square root of 4", 2.0, Math.sqrt(4), 0.001);
        assertEquals("Square root of 9", 3.0, Math.sqrt(9), 0.001);
        assertEquals("Square root of 0", 0.0, Math.sqrt(0), 0.001);

        // Test square root of 1
        assertEquals("Square root of 1", 1.0, Math.sqrt(1), 0.001);
    }

    @Test
    public void testPower() {
        // Test power operations
        assertEquals("2 to the power of 3", 8.0, Math.pow(2, 3), 0.001);
        assertEquals("10 to the power of 2", 100.0, Math.pow(10, 2), 0.001);
        assertEquals("Any number to the power of 0", 1.0, Math.pow(5, 0), 0.001);
        assertEquals("Any number to the power of 1", 5.0, Math.pow(5, 1), 0.001);
    }

    @Test
    public void testTrigonometry() {
        // Test basic trigonometric functions
        assertEquals("Sin of 0", 0.0, Math.sin(0), 0.001);
        assertEquals("Cos of 0", 1.0, Math.cos(0), 0.001);
        assertEquals("Tan of 0", 0.0, Math.tan(0), 0.001);

        // Test sin of π/2 (90 degrees)
        assertEquals("Sin of π/2", 1.0, Math.sin(Math.PI / 2), 0.001);
        assertEquals("Cos of π/2", 0.0, Math.cos(Math.PI / 2), 0.001);
    }

    @Test
    public void testLogarithm() {
        // Test logarithmic functions
        assertEquals("Natural log of e", 1.0, Math.log(Math.E), 0.001);
        assertEquals("Log base 10 of 10", 1.0, Math.log10(10), 0.001);
        assertEquals("Log base 10 of 100", 2.0, Math.log10(100), 0.001);
    }

    @Test
    public void testCeiling() {
        // Test ceiling function
        assertEquals("Ceiling of 4.1", 5.0, Math.ceil(4.1), 0.001);
        assertEquals("Ceiling of 4.0", 4.0, Math.ceil(4.0), 0.001);
        assertEquals("Ceiling of -4.1", -4.0, Math.ceil(-4.1), 0.001);
    }

    @Test
    public void testFloor() {
        // Test floor function
        assertEquals("Floor of 4.9", 4.0, Math.floor(4.9), 0.001);
        assertEquals("Floor of 4.0", 4.0, Math.floor(4.0), 0.001);
        assertEquals("Floor of -4.1", -5.0, Math.floor(-4.1), 0.001);
    }

    @Test
    public void testRandomRange() {
        // Test that random values are within expected range
        for (int i = 0; i < 100; i++) {
            double randomValue = Math.random();
            assertTrue("Random value should be >= 0", randomValue >= 0.0);
            assertTrue("Random value should be < 1", randomValue < 1.0);
        }
    }

    @Test
    public void testDistanceCalculation() {
        // Test distance calculation (Pythagorean theorem)
        double x1 = 0, y1 = 0;
        double x2 = 3, y2 = 4;

        double distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        assertEquals("Distance calculation", 5.0, distance, 0.001);
    }

    @Test
    public void testAngleConversion() {
        // Test angle conversion between degrees and radians
        double degrees = 90;
        double radians = Math.toRadians(degrees);
        double backToDegrees = Math.toDegrees(radians);

        assertEquals("Degrees to radians", Math.PI / 2, radians, 0.001);
        assertEquals("Radians back to degrees", degrees, backToDegrees, 0.001);
    }

    @Test
    public void testSpecialValues() {
        // Test special mathematical values
        assertTrue("Positive infinity", Double.isInfinite(Double.POSITIVE_INFINITY));
        assertTrue("Negative infinity", Double.isInfinite(Double.NEGATIVE_INFINITY));
        assertTrue("NaN", Double.isNaN(Double.NaN));

        // Test operations with special values
        assertTrue("0/0 is NaN", Double.isNaN(0.0 / 0.0));
        assertTrue("1/0 is positive infinity", Double.isInfinite(1.0 / 0.0));
        assertTrue("-1/0 is negative infinity", Double.isInfinite(-1.0 / 0.0));
    }

    @Test
    public void testNumberComparison() {
        // Test number comparison
        assertTrue("5 > 3", 5 > 3);
        assertTrue("3 < 5", 3 < 5);
        assertTrue("5 >= 5", 5 >= 5);
        assertTrue("5 <= 5", 5 <= 5);
        assertTrue("5 == 5", 5 == 5);
        assertTrue("5 != 3", 5 != 3);
    }

    @Test
    public void testIntegerDivision() {
        // Test integer division vs floating point division
        assertEquals("Integer division", 2, 7 / 3);
        assertEquals("Floating point division", 2.333, 7.0 / 3.0, 0.001);
        assertEquals("Modulo operation", 1, 7 % 3);
    }

    @Test
    public void testSignum() {
        // Test signum function
        assertEquals("Signum of positive", 1.0, Math.signum(5.5), 0.001);
        assertEquals("Signum of negative", -1.0, Math.signum(-5.5), 0.001);
        assertEquals("Signum of zero", 0.0, Math.signum(0.0), 0.001);
    }

    @Test
    public void testUlp() {
        // Test unit in the last place (ULP)
        double value = 1.0;
        double ulp = Math.ulp(value);

        assertTrue("ULP should be positive", ulp > 0);
        assertTrue("ULP should be very small", ulp < 1e-10);
    }

    @Test
    public void testHypot() {
        // Test hypotenuse calculation
        double a = 3.0;
        double b = 4.0;
        double hypotenuse = Math.hypot(a, b);

        assertEquals("Hypotenuse calculation", 5.0, hypotenuse, 0.001);
    }

    @Test
    public void testExponential() {
        // Test exponential function
        assertEquals("e^0", 1.0, Math.exp(0), 0.001);
        assertEquals("e^1", Math.E, Math.exp(1), 0.001);

        // Test expm1 (more accurate for small values)
        assertEquals("expm1(0)", 0.0, Math.expm1(0), 0.001);
    }

    @Test
    public void testNextAfter() {
        // Test nextAfter function
        double value = 1.0;
        double nextUp = Math.nextAfter(value, Double.POSITIVE_INFINITY);
        double nextDown = Math.nextAfter(value, Double.NEGATIVE_INFINITY);

        assertTrue("Next up should be greater", nextUp > value);
        assertTrue("Next down should be smaller", nextDown < value);
    }

    @Test
    public void testScalb() {
        // Test scalb function (multiply by power of 2)
        double value = 1.5;
        double scaled = Math.scalb(value, 2); // 1.5 * 2^2 = 6.0

        assertEquals("Scalb operation", 6.0, scaled, 0.001);
    }

    @Test
    public void testGetExponent() {
        // Test getExponent function
        double value = 8.0; // 2^3
        int exponent = Math.getExponent(value);

        assertEquals("Exponent of 8.0", 3, exponent);
    }

    @Test
    public void testCopySign() {
        // Test copySign function
        double magnitude = 5.0;
        double sign = -1.0;
        double result = Math.copySign(magnitude, sign);

        assertEquals("Copy sign", -5.0, result, 0.001);
    }
}
