package com.fj.towercontrol.util;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * 字符串工具类测试
 *
 * <AUTHOR>
 */
public class StringUtilsTest {

    @Test
    public void testStringFormat() {
        // Test basic string formatting
        String template = "Hello %s, you have %d messages";
        String result = String.format(template, "John", 5);

        assertEquals("String format should work correctly",
            "Hello John, you have 5 messages", result);
    }

    @Test
    public void testStringFormatWithFloats() {
        // Test formatting with floating point numbers
        String template = "Temperature: %.2f°C, Humidity: %.1f%%";
        String result = String.format(template, 23.456, 67.89);

        assertEquals("Float formatting should work correctly",
            "Temperature: 23.46°C, Humidity: 67.9%", result);
    }

    @Test
    public void testStringFormatWithNullValues() {
        // Test formatting with null values
        String template = "Name: %s, Age: %s";
        String result = String.format(template, null, null);

        assertEquals("Null values should be handled",
            "Name: null, Age: null", result);
    }

    @Test
    public void testStringFormatWithEmptyString() {
        // Test formatting with empty strings
        String template = "Value: '%s'";
        String result = String.format(template, "");

        assertEquals("Empty string should be handled",
            "Value: ''", result);
    }

    @Test
    public void testStringFormatWithSpecialCharacters() {
        // Test formatting with special characters
        String template = "Message: %s";
        String specialChars = "Special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?";
        String result = String.format(template, specialChars);

        assertEquals("Special characters should be preserved",
            "Message: " + specialChars, result);
    }

    @Test
    public void testStringFormatWithUnicode() {
        // Test formatting with Unicode characters
        String template = "系统状态: %s";
        String status = "正常运行 ✓";
        String result = String.format(template, status);

        assertEquals("Unicode characters should be preserved",
            "系统状态: 正常运行 ✓", result);
    }

    @Test
    public void testStringFormatWithMultipleTypes() {
        // Test formatting with multiple data types
        String template = "User: %s, ID: %d, Score: %.2f, Active: %b";
        String result = String.format(template, "Alice", 123, 95.67, true);

        assertEquals("Multiple types should be formatted correctly",
            "User: Alice, ID: 123, Score: 95.67, Active: true", result);
    }

    @Test
    public void testStringFormatWithLargeNumbers() {
        // Test formatting with large numbers
        String template = "Large number: %d";
        String result = String.format(template, 1234567890);

        // Note: Comma formatting depends on locale, so we test without comma
        assertNotNull("Large number formatting should not be null", result);
        assertTrue("Result should contain the number", result.contains("1234567890"));
        assertEquals("Large number should be formatted correctly",
            "Large number: 1234567890", result);
    }

    @Test
    public void testStringFormatWithPadding() {
        // Test formatting with padding
        String template = "Padded: '%10s'";
        String result = String.format(template, "test");

        assertEquals("Padding should work correctly",
            "Padded: '      test'", result);
    }

    @Test
    public void testStringFormatWithLeftAlign() {
        // Test formatting with left alignment
        String template = "Left aligned: '%-10s'";
        String result = String.format(template, "test");

        assertEquals("Left alignment should work correctly",
            "Left aligned: 'test      '", result);
    }

    @Test
    public void testStringFormatWithZeroPadding() {
        // Test formatting with zero padding for numbers
        String template = "Zero padded: %05d";
        String result = String.format(template, 42);

        assertEquals("Zero padding should work correctly",
            "Zero padded: 00042", result);
    }

    @Test
    public void testStringFormatWithHexadecimal() {
        // Test formatting with hexadecimal
        String template = "Hex: 0x%X";
        String result = String.format(template, 255);

        assertEquals("Hexadecimal formatting should work correctly",
            "Hex: 0xFF", result);
    }

    @Test
    public void testStringFormatWithScientificNotation() {
        // Test formatting with scientific notation
        String template = "Scientific: %e";
        String result = String.format(template, 1234.5678);

        assertNotNull("Scientific notation should not be null", result);
        assertTrue("Result should contain 'e'", result.toLowerCase().contains("e"));
    }

    @Test
    public void testStringFormatWithPercentage() {
        // Test formatting with percentage
        String template = "Percentage: %.1f%%";
        String result = String.format(template, 85.6);

        assertEquals("Percentage formatting should work correctly",
            "Percentage: 85.6%", result);
    }

    @Test
    public void testStringFormatWithDate() {
        // Test formatting with date/time components
        String template = "Time: %02d:%02d:%02d";
        String result = String.format(template, 9, 5, 3);

        assertEquals("Time formatting should work correctly",
            "Time: 09:05:03", result);
    }

    @Test
    public void testStringFormatWithComplexTemplate() {
        // Test a complex template similar to what might be used in the app
        String template = """
            ECU状态：
            父模式：%s，子模式：%s，塔吊当前状态：%s，
            错误码高位：%s，错误码低位：%s，
            工作半径：%s，塔身倾斜度：%s，吊钩离地高度：%s""";

        String result = String.format(template,
            1, 2, 3, "0x00", "0x00", "15.5m", "0.2°", "25.3m");

        assertNotNull("Complex template should not be null", result);
        assertTrue("Result should contain all values",
            result.contains("1") && result.contains("2") && result.contains("3"));
        assertTrue("Result should contain formatted values",
            result.contains("15.5m") && result.contains("0.2°") && result.contains("25.3m"));
    }

    @Test
    public void testStringFormatWithMissingArguments() {
        // Test what happens with missing arguments
        String template = "Value1: %s, Value2: %s";

        try {
            String result = String.format(template, "only one value");
            fail("Should throw exception for missing arguments");
        } catch (Exception e) {
            // Expected behavior - should throw an exception
            assertTrue("Should throw formatting exception", true);
        }
    }

    @Test
    public void testStringFormatWithExtraArguments() {
        // Test what happens with extra arguments
        String template = "Value: %s";
        String result = String.format(template, "value1", "value2", "value3");

        assertEquals("Extra arguments should be ignored",
            "Value: value1", result);
    }

    @Test
    public void testStringFormatWithInvalidFormat() {
        // Test what happens with invalid format specifiers
        String template = "Invalid: %z";

        try {
            String result = String.format(template, "test");
            fail("Should throw exception for invalid format");
        } catch (Exception e) {
            // Expected behavior - should throw an exception
            assertTrue("Should throw formatting exception", true);
        }
    }

    @Test
    public void testStringIsEmpty() {
        // Test empty string checks
        assertTrue("Empty string should be empty", "".isEmpty());
        assertFalse("Non-empty string should not be empty", "test".isEmpty());
    }

    @Test
    public void testStringIsBlank() {
        // Test blank string checks (whitespace only)
        assertTrue("Whitespace string should be blank", "   ".trim().isEmpty());
        assertTrue("Tab string should be blank", "\t\t".trim().isEmpty());
        assertTrue("Newline string should be blank", "\n\n".trim().isEmpty());
        assertFalse("String with content should not be blank", " test ".trim().isEmpty());
    }

    @Test
    public void testStringTrim() {
        // Test string trimming
        assertEquals("Leading spaces should be trimmed", "test", "   test".trim());
        assertEquals("Trailing spaces should be trimmed", "test", "test   ".trim());
        assertEquals("Both sides should be trimmed", "test", "   test   ".trim());
        assertEquals("Internal spaces should be preserved", "te st", "   te st   ".trim());
    }

    @Test
    public void testStringLength() {
        // Test string length calculations
        assertEquals("Empty string length should be 0", 0, "".length());
        assertEquals("Single char length should be 1", 1, "a".length());
        assertEquals("Multi char length should be correct", 5, "hello".length());
        assertEquals("Unicode char length should be correct", 3, "你好世".length());
    }

    @Test
    public void testStringContains() {
        // Test string contains functionality
        String text = "Hello World";

        assertTrue("Should contain substring", text.contains("Hello"));
        assertTrue("Should contain substring", text.contains("World"));
        assertTrue("Should contain substring", text.contains("llo Wo"));
        assertFalse("Should not contain substring", text.contains("hello")); // case sensitive
        assertFalse("Should not contain substring", text.contains("xyz"));
    }

    @Test
    public void testStringStartsEndsWith() {
        // Test string starts/ends with functionality
        String text = "Hello World";

        assertTrue("Should start with prefix", text.startsWith("Hello"));
        assertTrue("Should end with suffix", text.endsWith("World"));
        assertFalse("Should not start with wrong prefix", text.startsWith("hello"));
        assertFalse("Should not end with wrong suffix", text.endsWith("world"));
    }
}
