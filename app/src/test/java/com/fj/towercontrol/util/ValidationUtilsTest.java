package com.fj.towercontrol.util;

import static org.junit.Assert.*;

import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 数据验证工具类测试
 * 
 * <AUTHOR>
 */
public class ValidationUtilsTest {

    @Test
    public void testStringValidation() {
        // Test null string
        String nullString = null;
        assertTrue("Null string should be invalid", nullString == null);
        
        // Test empty string
        String emptyString = "";
        assertTrue("Empty string should be empty", emptyString.isEmpty());
        
        // Test whitespace string
        String whitespaceString = "   ";
        assertTrue("Whitespace string should be blank", whitespaceString.trim().isEmpty());
        
        // Test valid string
        String validString = "Hello World";
        assertFalse("Valid string should not be empty", validString.isEmpty());
        assertFalse("Valid string should not be blank", validString.trim().isEmpty());
    }

    @Test
    public void testNumberValidation() {
        // Test positive numbers
        assertTrue("Positive integer should be positive", 5 > 0);
        assertTrue("Positive double should be positive", 5.5 > 0);
        
        // Test negative numbers
        assertTrue("Negative integer should be negative", -5 < 0);
        assertTrue("Negative double should be negative", -5.5 < 0);
        
        // Test zero
        assertTrue("Zero should equal zero", 0 == 0);
        assertTrue("Zero double should equal zero", 0.0 == 0.0);
    }

    @Test
    public void testRangeValidation() {
        // Test battery percentage range (0-100)
        assertTrue("0% should be valid", isValidBatteryPercentage(0.0));
        assertTrue("50% should be valid", isValidBatteryPercentage(50.0));
        assertTrue("100% should be valid", isValidBatteryPercentage(100.0));
        
        assertFalse("Negative percentage should be invalid", isValidBatteryPercentage(-1.0));
        assertFalse("Over 100% should be invalid", isValidBatteryPercentage(101.0));
    }

    @Test
    public void testBatteryStatusValidation() {
        // Test valid battery status values: 0静止，1充电，2放电
        assertTrue("Status 0 should be valid", isValidBatteryStatus(0));
        assertTrue("Status 1 should be valid", isValidBatteryStatus(1));
        assertTrue("Status 2 should be valid", isValidBatteryStatus(2));
        
        assertFalse("Status -1 should be invalid", isValidBatteryStatus(-1));
        assertFalse("Status 3 should be invalid", isValidBatteryStatus(3));
    }

    @Test
    public void testListValidation() {
        // Test null list
        List<String> nullList = null;
        assertTrue("Null list should be null", nullList == null);
        
        // Test empty list
        List<String> emptyList = new ArrayList<>();
        assertTrue("Empty list should be empty", emptyList.isEmpty());
        
        // Test non-empty list
        List<String> nonEmptyList = Arrays.asList("item1", "item2");
        assertFalse("Non-empty list should not be empty", nonEmptyList.isEmpty());
        assertEquals("List size should be 2", 2, nonEmptyList.size());
    }

    @Test
    public void testArrayValidation() {
        // Test null array
        String[] nullArray = null;
        assertTrue("Null array should be null", nullArray == null);
        
        // Test empty array
        String[] emptyArray = new String[0];
        assertEquals("Empty array length should be 0", 0, emptyArray.length);
        
        // Test non-empty array
        String[] nonEmptyArray = {"item1", "item2"};
        assertEquals("Array length should be 2", 2, nonEmptyArray.length);
        assertNotNull("Array element should not be null", nonEmptyArray[0]);
    }

    @Test
    public void testEmailValidation() {
        // Simple email validation tests
        assertTrue("Valid email should pass", isValidEmailFormat("<EMAIL>"));
        assertTrue("Valid email with subdomain should pass", isValidEmailFormat("<EMAIL>"));
        
        assertFalse("Email without @ should fail", isValidEmailFormat("testexample.com"));
        assertFalse("Email without domain should fail", isValidEmailFormat("test@"));
        assertFalse("Email without local part should fail", isValidEmailFormat("@example.com"));
        assertFalse("Empty email should fail", isValidEmailFormat(""));
        assertFalse("Null email should fail", isValidEmailFormat(null));
    }

    @Test
    public void testPhoneNumberValidation() {
        // Simple phone number validation tests
        assertTrue("Valid phone number should pass", isValidPhoneNumber("13812345678"));
        assertTrue("Valid phone with dashes should pass", isValidPhoneNumber("138-1234-5678"));
        assertTrue("Valid phone with spaces should pass", isValidPhoneNumber("138 1234 5678"));
        
        assertFalse("Too short phone should fail", isValidPhoneNumber("123"));
        assertFalse("Too long phone should fail", isValidPhoneNumber("123456789012345"));
        assertFalse("Phone with letters should fail", isValidPhoneNumber("138abcd5678"));
        assertFalse("Empty phone should fail", isValidPhoneNumber(""));
        assertFalse("Null phone should fail", isValidPhoneNumber(null));
    }

    @Test
    public void testIPAddressValidation() {
        // Simple IP address validation tests
        assertTrue("Valid IP should pass", isValidIPAddress("***********"));
        assertTrue("Valid IP with zeros should pass", isValidIPAddress("***********"));
        assertTrue("Localhost IP should pass", isValidIPAddress("127.0.0.1"));
        
        assertFalse("IP with invalid octet should fail", isValidIPAddress("192.168.1.256"));
        assertFalse("IP with too few octets should fail", isValidIPAddress("192.168.1"));
        assertFalse("IP with too many octets should fail", isValidIPAddress("***********.1"));
        assertFalse("IP with letters should fail", isValidIPAddress("192.168.a.1"));
        assertFalse("Empty IP should fail", isValidIPAddress(""));
        assertFalse("Null IP should fail", isValidIPAddress(null));
    }

    @Test
    public void testPortValidation() {
        // Test valid port ranges (1-65535)
        assertTrue("Port 1 should be valid", isValidPort(1));
        assertTrue("Port 80 should be valid", isValidPort(80));
        assertTrue("Port 8080 should be valid", isValidPort(8080));
        assertTrue("Port 65535 should be valid", isValidPort(65535));
        
        assertFalse("Port 0 should be invalid", isValidPort(0));
        assertFalse("Port -1 should be invalid", isValidPort(-1));
        assertFalse("Port 65536 should be invalid", isValidPort(65536));
    }

    @Test
    public void testCoordinateValidation() {
        // Test latitude validation (-90 to 90)
        assertTrue("Latitude 0 should be valid", isValidLatitude(0.0));
        assertTrue("Latitude 45 should be valid", isValidLatitude(45.0));
        assertTrue("Latitude -45 should be valid", isValidLatitude(-45.0));
        assertTrue("Latitude 90 should be valid", isValidLatitude(90.0));
        assertTrue("Latitude -90 should be valid", isValidLatitude(-90.0));
        
        assertFalse("Latitude 91 should be invalid", isValidLatitude(91.0));
        assertFalse("Latitude -91 should be invalid", isValidLatitude(-91.0));
        
        // Test longitude validation (-180 to 180)
        assertTrue("Longitude 0 should be valid", isValidLongitude(0.0));
        assertTrue("Longitude 90 should be valid", isValidLongitude(90.0));
        assertTrue("Longitude -90 should be valid", isValidLongitude(-90.0));
        assertTrue("Longitude 180 should be valid", isValidLongitude(180.0));
        assertTrue("Longitude -180 should be valid", isValidLongitude(-180.0));
        
        assertFalse("Longitude 181 should be invalid", isValidLongitude(181.0));
        assertFalse("Longitude -181 should be invalid", isValidLongitude(-181.0));
    }

    @Test
    public void testDateValidation() {
        // Test timestamp validation
        long currentTime = System.currentTimeMillis();
        long pastTime = currentTime - 86400000; // 24 hours ago
        long futureTime = currentTime + 86400000; // 24 hours from now
        
        assertTrue("Current timestamp should be valid", isValidTimestamp(currentTime));
        assertTrue("Past timestamp should be valid", isValidTimestamp(pastTime));
        assertTrue("Future timestamp should be valid", isValidTimestamp(futureTime));
        
        assertFalse("Negative timestamp should be invalid", isValidTimestamp(-1));
        assertFalse("Zero timestamp should be invalid", isValidTimestamp(0));
    }

    @Test
    public void testFilePathValidation() {
        // Test file path validation
        assertTrue("Valid Unix path should pass", isValidFilePath("/home/<USER>/file.txt"));
        assertTrue("Valid Windows path should pass", isValidFilePath("C:\\Users\\<USER>\\file.txt"));
        assertTrue("Relative path should pass", isValidFilePath("./file.txt"));
        assertTrue("Current directory should pass", isValidFilePath("."));
        
        assertFalse("Empty path should fail", isValidFilePath(""));
        assertFalse("Null path should fail", isValidFilePath(null));
        assertFalse("Path with invalid characters should fail", isValidFilePath("file<>|.txt"));
    }

    // Helper methods for validation
    private boolean isValidBatteryPercentage(double percentage) {
        return percentage >= 0.0 && percentage <= 100.0;
    }

    private boolean isValidBatteryStatus(int status) {
        return status >= 0 && status <= 2;
    }

    private boolean isValidEmailFormat(String email) {
        if (email == null || email.isEmpty()) {
            return false;
        }
        return email.contains("@") && email.indexOf("@") > 0 && email.indexOf("@") < email.length() - 1;
    }

    private boolean isValidPhoneNumber(String phone) {
        if (phone == null || phone.isEmpty()) {
            return false;
        }
        String cleanPhone = phone.replaceAll("[\\s-]", "");
        return cleanPhone.matches("\\d{11}") && cleanPhone.length() >= 10 && cleanPhone.length() <= 15;
    }

    private boolean isValidIPAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        for (String part : parts) {
            try {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return true;
    }

    private boolean isValidPort(int port) {
        return port > 0 && port <= 65535;
    }

    private boolean isValidLatitude(double latitude) {
        return latitude >= -90.0 && latitude <= 90.0;
    }

    private boolean isValidLongitude(double longitude) {
        return longitude >= -180.0 && longitude <= 180.0;
    }

    private boolean isValidTimestamp(long timestamp) {
        return timestamp > 0;
    }

    private boolean isValidFilePath(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }
        // Simple validation - no invalid characters
        return !path.matches(".*[<>|\"*?].*");
    }
}
