package com.fj.towercontrol.data.entity;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * Vibration实体类测试
 * 
 * <AUTHOR>
 */
public class VibrationTest {

    @Test
    public void testDefaultConstructor() {
        Vibration vibration = new Vibration();
        
        assertNotNull("Vibration should not be null", vibration);
        assertEquals("Default ax should be 0", 0.0, vibration.getAx(), 0.000001);
        assertEquals("Default ay should be 0", 0.0, vibration.getAy(), 0.000001);
        assertEquals("Default az should be 0", 0.0, vibration.getAz(), 0.000001);
    }

    @Test
    public void testParameterizedConstructor() {
        double ax = 1.5;
        double ay = 2.3;
        double az = 0.8;
        
        Vibration vibration = new Vibration(ax, ay, az);
        
        assertNotNull("Vibration should not be null", vibration);
        assertEquals("Ax should match", ax, vibration.getAx(), 0.000001);
        assertEquals("Ay should match", ay, vibration.getAy(), 0.000001);
        assertEquals("Az should match", az, vibration.getAz(), 0.000001);
    }

    @Test
    public void testAxGetterSetter() {
        Vibration vibration = new Vibration();
        double testAx = 2.5;
        
        vibration.setAx(testAx);
        double retrievedAx = vibration.getAx();
        
        assertEquals("Ax should match", testAx, retrievedAx, 0.000001);
    }

    @Test
    public void testAyGetterSetter() {
        Vibration vibration = new Vibration();
        double testAy = 3.7;
        
        vibration.setAy(testAy);
        double retrievedAy = vibration.getAy();
        
        assertEquals("Ay should match", testAy, retrievedAy, 0.000001);
    }

    @Test
    public void testAzGetterSetter() {
        Vibration vibration = new Vibration();
        double testAz = 1.2;
        
        vibration.setAz(testAz);
        double retrievedAz = vibration.getAz();
        
        assertEquals("Az should match", testAz, retrievedAz, 0.000001);
    }

    @Test
    public void testZeroValues() {
        Vibration vibration = new Vibration(0.0, 0.0, 0.0);
        
        assertEquals("Zero ax should be handled", 0.0, vibration.getAx(), 0.000001);
        assertEquals("Zero ay should be handled", 0.0, vibration.getAy(), 0.000001);
        assertEquals("Zero az should be handled", 0.0, vibration.getAz(), 0.000001);
    }

    @Test
    public void testNegativeValues() {
        double negativeAx = -1.5;
        double negativeAy = -2.3;
        double negativeAz = -0.8;
        
        Vibration vibration = new Vibration(negativeAx, negativeAy, negativeAz);
        
        assertEquals("Negative ax should be handled", negativeAx, vibration.getAx(), 0.000001);
        assertEquals("Negative ay should be handled", negativeAy, vibration.getAy(), 0.000001);
        assertEquals("Negative az should be handled", negativeAz, vibration.getAz(), 0.000001);
    }

    @Test
    public void testLargeValues() {
        double largeAx = 1000.123456;
        double largeAy = 2000.654321;
        double largeAz = 3000.987654;
        
        Vibration vibration = new Vibration(largeAx, largeAy, largeAz);
        
        assertEquals("Large ax should be handled", largeAx, vibration.getAx(), 0.000001);
        assertEquals("Large ay should be handled", largeAy, vibration.getAy(), 0.000001);
        assertEquals("Large az should be handled", largeAz, vibration.getAz(), 0.000001);
    }

    @Test
    public void testSmallValues() {
        double smallAx = 0.000001;
        double smallAy = 0.000002;
        double smallAz = 0.000003;
        
        Vibration vibration = new Vibration(smallAx, smallAy, smallAz);
        
        assertEquals("Small ax should be handled", smallAx, vibration.getAx(), 0.0000000001);
        assertEquals("Small ay should be handled", smallAy, vibration.getAy(), 0.0000000001);
        assertEquals("Small az should be handled", smallAz, vibration.getAz(), 0.0000000001);
    }

    @Test
    public void testMultipleUpdates() {
        Vibration vibration = new Vibration();
        
        double[] axValues = {1.0, 2.5, 3.7, 4.2, 5.8};
        double[] ayValues = {0.5, 1.3, 2.1, 3.9, 4.6};
        double[] azValues = {0.2, 0.8, 1.4, 2.0, 2.7};
        
        for (int i = 0; i < axValues.length; i++) {
            vibration.setAx(axValues[i]);
            vibration.setAy(ayValues[i]);
            vibration.setAz(azValues[i]);
            
            assertEquals("Ax should match after update " + i, 
                axValues[i], vibration.getAx(), 0.000001);
            assertEquals("Ay should match after update " + i, 
                ayValues[i], vibration.getAy(), 0.000001);
            assertEquals("Az should match after update " + i, 
                azValues[i], vibration.getAz(), 0.000001);
        }
    }

    @Test
    public void testObjectState() {
        double initialAx = 1.5;
        double initialAy = 2.3;
        double initialAz = 0.8;
        
        Vibration vibration = new Vibration(initialAx, initialAy, initialAz);
        
        // Verify initial state
        assertEquals("Initial ax should match", initialAx, vibration.getAx(), 0.000001);
        assertEquals("Initial ay should match", initialAy, vibration.getAy(), 0.000001);
        assertEquals("Initial az should match", initialAz, vibration.getAz(), 0.000001);
        
        // Change one property
        double newAx = 3.5;
        vibration.setAx(newAx);
        
        // Verify only the changed property is updated
        assertEquals("Ax should be updated", newAx, vibration.getAx(), 0.000001);
        assertEquals("Ay should remain unchanged", initialAy, vibration.getAy(), 0.000001);
        assertEquals("Az should remain unchanged", initialAz, vibration.getAz(), 0.000001);
        
        // Change another property
        double newAy = 4.7;
        vibration.setAy(newAy);
        
        // Verify the state
        assertEquals("Ax should remain as updated", newAx, vibration.getAx(), 0.000001);
        assertEquals("Ay should be updated", newAy, vibration.getAy(), 0.000001);
        assertEquals("Az should remain unchanged", initialAz, vibration.getAz(), 0.000001);
    }

    @Test
    public void testVibrationMagnitude() {
        // Test vibration magnitude calculation (if needed for analysis)
        double ax = 3.0;
        double ay = 4.0;
        double az = 0.0;
        
        Vibration vibration = new Vibration(ax, ay, az);
        
        // Calculate magnitude: sqrt(ax² + ay² + az²)
        double expectedMagnitude = Math.sqrt(ax * ax + ay * ay + az * az); // 5.0
        double actualMagnitude = Math.sqrt(
            vibration.getAx() * vibration.getAx() + 
            vibration.getAy() * vibration.getAy() + 
            vibration.getAz() * vibration.getAz()
        );
        
        assertEquals("Vibration magnitude should be correct", 
            expectedMagnitude, actualMagnitude, 0.000001);
    }

    @Test
    public void testVibrationEquality() {
        Vibration vibration1 = new Vibration(1.5, 2.3, 0.8);
        Vibration vibration2 = new Vibration(1.5, 2.3, 0.8);
        Vibration vibration3 = new Vibration(1.6, 2.3, 0.8);
        
        // Note: This test assumes Vibration doesn't override equals()
        // Test that objects with same data have same field values
        assertEquals("Same ax values should match", vibration1.getAx(), vibration2.getAx(), 0.000001);
        assertEquals("Same ay values should match", vibration1.getAy(), vibration2.getAy(), 0.000001);
        assertEquals("Same az values should match", vibration1.getAz(), vibration2.getAz(), 0.000001);
        
        // Test that objects with different data have different field values
        assertNotEquals("Different ax values should not match", 
            vibration1.getAx(), vibration3.getAx(), 0.000001);
    }

    @Test
    public void testToString() {
        Vibration vibration = new Vibration(1.5, 2.3, 0.8);
        
        String toStringResult = vibration.toString();
        
        assertNotNull("toString() should not return null", toStringResult);
        assertTrue("toString() should not be empty", toStringResult.length() > 0);
        assertTrue("toString() should contain 'Vibration'", toStringResult.contains("Vibration"));
        assertTrue("toString() should contain ax value", toStringResult.contains("1.5"));
        assertTrue("toString() should contain ay value", toStringResult.contains("2.3"));
        assertTrue("toString() should contain az value", toStringResult.contains("0.8"));
    }

    @Test
    public void testExtremeValues() {
        // Test with extreme double values
        Vibration vibrationMax = new Vibration(Double.MAX_VALUE, Double.MAX_VALUE, Double.MAX_VALUE);
        assertEquals("Max ax value should be handled", Double.MAX_VALUE, vibrationMax.getAx(), 0.0);
        assertEquals("Max ay value should be handled", Double.MAX_VALUE, vibrationMax.getAy(), 0.0);
        assertEquals("Max az value should be handled", Double.MAX_VALUE, vibrationMax.getAz(), 0.0);
        
        Vibration vibrationMin = new Vibration(Double.MIN_VALUE, Double.MIN_VALUE, Double.MIN_VALUE);
        assertEquals("Min ax value should be handled", Double.MIN_VALUE, vibrationMin.getAx(), 0.0);
        assertEquals("Min ay value should be handled", Double.MIN_VALUE, vibrationMin.getAy(), 0.0);
        assertEquals("Min az value should be handled", Double.MIN_VALUE, vibrationMin.getAz(), 0.0);
    }

    @Test
    public void testSpecialDoubleValues() {
        // Test with special double values
        Vibration vibrationInf = new Vibration(Double.POSITIVE_INFINITY, Double.NEGATIVE_INFINITY, Double.POSITIVE_INFINITY);
        assertEquals("Positive infinity ax should be preserved", 
            Double.POSITIVE_INFINITY, vibrationInf.getAx(), 0.0);
        assertEquals("Negative infinity ay should be preserved", 
            Double.NEGATIVE_INFINITY, vibrationInf.getAy(), 0.0);
        assertEquals("Positive infinity az should be preserved", 
            Double.POSITIVE_INFINITY, vibrationInf.getAz(), 0.0);
        
        Vibration vibrationNaN = new Vibration(Double.NaN, Double.NaN, Double.NaN);
        assertTrue("NaN ax should be preserved", Double.isNaN(vibrationNaN.getAx()));
        assertTrue("NaN ay should be preserved", Double.isNaN(vibrationNaN.getAy()));
        assertTrue("NaN az should be preserved", Double.isNaN(vibrationNaN.getAz()));
    }

    @Test
    public void testRealWorldVibrationScenario() {
        // Test real-world vibration scenarios for tower crane
        
        // Low vibration (normal operation)
        Vibration lowVibration = new Vibration(0.1, 0.15, 0.08);
        assertTrue("Low vibration ax should be small", Math.abs(lowVibration.getAx()) < 0.5);
        assertTrue("Low vibration ay should be small", Math.abs(lowVibration.getAy()) < 0.5);
        assertTrue("Low vibration az should be small", Math.abs(lowVibration.getAz()) < 0.5);
        
        // Medium vibration (working condition)
        Vibration mediumVibration = new Vibration(0.8, 1.2, 0.6);
        assertTrue("Medium vibration ax should be moderate", 
            Math.abs(mediumVibration.getAx()) >= 0.5 && Math.abs(mediumVibration.getAx()) < 2.0);
        assertTrue("Medium vibration ay should be moderate", 
            Math.abs(mediumVibration.getAy()) >= 0.5 && Math.abs(mediumVibration.getAy()) < 2.0);
        assertTrue("Medium vibration az should be moderate", 
            Math.abs(mediumVibration.getAz()) >= 0.5 && Math.abs(mediumVibration.getAz()) < 2.0);
        
        // High vibration (warning condition)
        Vibration highVibration = new Vibration(2.5, 3.1, 2.8);
        assertTrue("High vibration ax should be significant", Math.abs(highVibration.getAx()) >= 2.0);
        assertTrue("High vibration ay should be significant", Math.abs(highVibration.getAy()) >= 2.0);
        assertTrue("High vibration az should be significant", Math.abs(highVibration.getAz()) >= 2.0);
    }

    @Test
    public void testVibrationConsistency() {
        // Test that vibration values remain consistent across operations
        Vibration vibration = new Vibration();
        
        for (int i = 0; i < 100; i++) {
            double ax = i * 0.01;
            double ay = i * 0.02;
            double az = i * 0.005;
            
            vibration.setAx(ax);
            vibration.setAy(ay);
            vibration.setAz(az);
            
            assertEquals("Ax should be consistent at iteration " + i, 
                ax, vibration.getAx(), 0.000001);
            assertEquals("Ay should be consistent at iteration " + i, 
                ay, vibration.getAy(), 0.000001);
            assertEquals("Az should be consistent at iteration " + i, 
                az, vibration.getAz(), 0.000001);
        }
    }

    @Test
    public void testVibrationThresholds() {
        // Test vibration threshold scenarios
        double normalThreshold = 1.0;
        double warningThreshold = 2.0;
        double alarmThreshold = 3.0;
        
        // Normal vibration
        Vibration normalVibration = new Vibration(0.5, 0.7, 0.3);
        double normalMagnitude = Math.sqrt(
            normalVibration.getAx() * normalVibration.getAx() + 
            normalVibration.getAy() * normalVibration.getAy() + 
            normalVibration.getAz() * normalVibration.getAz()
        );
        assertTrue("Normal vibration should be below threshold", normalMagnitude < normalThreshold);
        
        // Warning vibration
        Vibration warningVibration = new Vibration(1.2, 1.5, 1.0);
        double warningMagnitude = Math.sqrt(
            warningVibration.getAx() * warningVibration.getAx() + 
            warningVibration.getAy() * warningVibration.getAy() + 
            warningVibration.getAz() * warningVibration.getAz()
        );
        assertTrue("Warning vibration should be above normal but below alarm", 
            warningMagnitude >= normalThreshold && warningMagnitude < alarmThreshold);
        
        // Alarm vibration
        Vibration alarmVibration = new Vibration(2.5, 3.0, 2.0);
        double alarmMagnitude = Math.sqrt(
            alarmVibration.getAx() * alarmVibration.getAx() + 
            alarmVibration.getAy() * alarmVibration.getAy() + 
            alarmVibration.getAz() * alarmVibration.getAz()
        );
        assertTrue("Alarm vibration should be above threshold", alarmMagnitude >= alarmThreshold);
    }

    @Test
    public void testVibrationDataIntegrity() {
        // Test that vibration data maintains integrity
        double originalAx = 1.234567890123456;
        double originalAy = 2.345678901234567;
        double originalAz = 3.456789012345678;
        
        Vibration vibration = new Vibration(originalAx, originalAy, originalAz);
        
        // Verify precision is maintained
        assertEquals("High precision ax should be maintained", 
            originalAx, vibration.getAx(), 0.000000000000001);
        assertEquals("High precision ay should be maintained", 
            originalAy, vibration.getAy(), 0.000000000000001);
        assertEquals("High precision az should be maintained", 
            originalAz, vibration.getAz(), 0.000000000000001);
        
        // Test multiple reads don't affect values
        for (int i = 0; i < 10; i++) {
            assertEquals("Ax should remain consistent after multiple reads", 
                originalAx, vibration.getAx(), 0.000000000000001);
            assertEquals("Ay should remain consistent after multiple reads", 
                originalAy, vibration.getAy(), 0.000000000000001);
            assertEquals("Az should remain consistent after multiple reads", 
                originalAz, vibration.getAz(), 0.000000000000001);
        }
    }
}
