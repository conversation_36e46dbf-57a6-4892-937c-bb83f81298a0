package com.fj.towercontrol.data.entity;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * HistoryPoint实体类测试
 * 
 * <AUTHOR>
 */
public class HistoryPointTest {

    @Test
    public void testDefaultConstructor() {
        HistoryPoint historyPoint = new HistoryPoint();
        
        assertNotNull("HistoryPoint should not be null", historyPoint);
        assertEquals("Default timestamp should be 0", 0L, historyPoint.getTimestamp());
        assertEquals("Default lat should be 0", 0.0, historyPoint.getLat(), 0.000001);
        assertEquals("Default lng should be 0", 0.0, historyPoint.getLng(), 0.000001);
        assertEquals("Default alt should be 0", 0.0, historyPoint.getAlt(), 0.000001);
        assertEquals("Default hookGear should be 0", 0, historyPoint.getHookGear());
        assertEquals("Default trolleyGear should be 0", 0, historyPoint.getTrolleyGear());
        assertEquals("Default spinGear should be 0", 0, historyPoint.getSpinGear());
    }

    @Test
    public void testParameterizedConstructor() {
        long timestamp = System.currentTimeMillis();
        double lat = 39.9042;
        double lng = 116.4074;
        double alt = 45.5;
        
        HistoryPoint historyPoint = new HistoryPoint(timestamp, lat, lng, alt);
        
        assertNotNull("HistoryPoint should not be null", historyPoint);
        assertEquals("Timestamp should match", timestamp, historyPoint.getTimestamp());
        assertEquals("Lat should match", lat, historyPoint.getLat(), 0.000001);
        assertEquals("Lng should match", lng, historyPoint.getLng(), 0.000001);
        assertEquals("Alt should match", alt, historyPoint.getAlt(), 0.000001);
        assertEquals("Default hookGear should be 0", 0, historyPoint.getHookGear());
        assertEquals("Default trolleyGear should be 0", 0, historyPoint.getTrolleyGear());
        assertEquals("Default spinGear should be 0", 0, historyPoint.getSpinGear());
    }

    @Test
    public void testTimestampGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        long testTimestamp = System.currentTimeMillis();
        
        historyPoint.setTimestamp(testTimestamp);
        long retrievedTimestamp = historyPoint.getTimestamp();
        
        assertEquals("Timestamp should match", testTimestamp, retrievedTimestamp);
    }

    @Test
    public void testLatGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        double testLat = 39.9042;
        
        historyPoint.setLat(testLat);
        double retrievedLat = historyPoint.getLat();
        
        assertEquals("Lat should match", testLat, retrievedLat, 0.000001);
    }

    @Test
    public void testLngGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        double testLng = 116.4074;
        
        historyPoint.setLng(testLng);
        double retrievedLng = historyPoint.getLng();
        
        assertEquals("Lng should match", testLng, retrievedLng, 0.000001);
    }

    @Test
    public void testAltGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        double testAlt = 45.5;
        
        historyPoint.setAlt(testAlt);
        double retrievedAlt = historyPoint.getAlt();
        
        assertEquals("Alt should match", testAlt, retrievedAlt, 0.000001);
    }

    @Test
    public void testHookGearGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        int testHookGear = 3;
        
        historyPoint.setHookGear(testHookGear);
        int retrievedHookGear = historyPoint.getHookGear();
        
        assertEquals("HookGear should match", testHookGear, retrievedHookGear);
    }

    @Test
    public void testTrolleyGearGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        int testTrolleyGear = 2;
        
        historyPoint.setTrolleyGear(testTrolleyGear);
        int retrievedTrolleyGear = historyPoint.getTrolleyGear();
        
        assertEquals("TrolleyGear should match", testTrolleyGear, retrievedTrolleyGear);
    }

    @Test
    public void testSpinGearGetterSetter() {
        HistoryPoint historyPoint = new HistoryPoint();
        int testSpinGear = 1;
        
        historyPoint.setSpinGear(testSpinGear);
        int retrievedSpinGear = historyPoint.getSpinGear();
        
        assertEquals("SpinGear should match", testSpinGear, retrievedSpinGear);
    }

    @Test
    public void testBeijingCoordinates() {
        // Test with Beijing coordinates
        long timestamp = 1640995200000L; // 2022-01-01 00:00:00 UTC
        double beijingLat = 39.9042;
        double beijingLng = 116.4074;
        double beijingAlt = 43.5;
        
        HistoryPoint beijing = new HistoryPoint(timestamp, beijingLat, beijingLng, beijingAlt);
        
        assertEquals("Beijing timestamp should match", timestamp, beijing.getTimestamp());
        assertEquals("Beijing latitude should match", beijingLat, beijing.getLat(), 0.000001);
        assertEquals("Beijing longitude should match", beijingLng, beijing.getLng(), 0.000001);
        assertEquals("Beijing altitude should match", beijingAlt, beijing.getAlt(), 0.000001);
    }

    @Test
    public void testNegativeCoordinates() {
        // Test with negative coordinates (southern/western hemispheres)
        long timestamp = System.currentTimeMillis();
        double southLat = -33.8688; // Sydney
        double westLng = -151.2093; // Sydney
        double negativeAlt = -10.0;  // Below sea level
        
        HistoryPoint negativePoint = new HistoryPoint(timestamp, southLat, westLng, negativeAlt);
        
        assertEquals("Negative latitude should be handled", southLat, negativePoint.getLat(), 0.000001);
        assertEquals("Negative longitude should be handled", westLng, negativePoint.getLng(), 0.000001);
        assertEquals("Negative altitude should be handled", negativeAlt, negativePoint.getAlt(), 0.000001);
    }

    @Test
    public void testZeroCoordinates() {
        // Test with zero coordinates (null island)
        long timestamp = System.currentTimeMillis();
        double zeroLat = 0.0;
        double zeroLng = 0.0;
        double zeroAlt = 0.0;
        
        HistoryPoint zeroPoint = new HistoryPoint(timestamp, zeroLat, zeroLng, zeroAlt);
        
        assertEquals("Zero latitude should be handled", zeroLat, zeroPoint.getLat(), 0.000001);
        assertEquals("Zero longitude should be handled", zeroLng, zeroPoint.getLng(), 0.000001);
        assertEquals("Zero altitude should be handled", zeroAlt, zeroPoint.getAlt(), 0.000001);
    }

    @Test
    public void testGearValues() {
        HistoryPoint historyPoint = new HistoryPoint();
        
        // Test positive gear values
        historyPoint.setHookGear(5);
        historyPoint.setTrolleyGear(3);
        historyPoint.setSpinGear(2);
        
        assertEquals("Positive hook gear should be handled", 5, historyPoint.getHookGear());
        assertEquals("Positive trolley gear should be handled", 3, historyPoint.getTrolleyGear());
        assertEquals("Positive spin gear should be handled", 2, historyPoint.getSpinGear());
        
        // Test negative gear values
        historyPoint.setHookGear(-1);
        historyPoint.setTrolleyGear(-2);
        historyPoint.setSpinGear(-3);
        
        assertEquals("Negative hook gear should be handled", -1, historyPoint.getHookGear());
        assertEquals("Negative trolley gear should be handled", -2, historyPoint.getTrolleyGear());
        assertEquals("Negative spin gear should be handled", -3, historyPoint.getSpinGear());
    }

    @Test
    public void testTimestampValues() {
        HistoryPoint historyPoint = new HistoryPoint();
        
        // Test current timestamp
        long currentTime = System.currentTimeMillis();
        historyPoint.setTimestamp(currentTime);
        assertEquals("Current timestamp should be handled", currentTime, historyPoint.getTimestamp());
        
        // Test past timestamp
        long pastTime = currentTime - 86400000L; // 24 hours ago
        historyPoint.setTimestamp(pastTime);
        assertEquals("Past timestamp should be handled", pastTime, historyPoint.getTimestamp());
        
        // Test future timestamp
        long futureTime = currentTime + 86400000L; // 24 hours from now
        historyPoint.setTimestamp(futureTime);
        assertEquals("Future timestamp should be handled", futureTime, historyPoint.getTimestamp());
        
        // Test zero timestamp
        historyPoint.setTimestamp(0L);
        assertEquals("Zero timestamp should be handled", 0L, historyPoint.getTimestamp());
    }

    @Test
    public void testHighPrecisionCoordinates() {
        // Test with high precision coordinates
        long timestamp = System.currentTimeMillis();
        double preciseLat = 39.904211123456789;
        double preciseLng = 116.407395987654321;
        double preciseAlt = 45.123456789;
        
        HistoryPoint precisePoint = new HistoryPoint(timestamp, preciseLat, preciseLng, preciseAlt);
        
        assertEquals("High precision latitude should be preserved", 
            preciseLat, precisePoint.getLat(), 0.000000000001);
        assertEquals("High precision longitude should be preserved", 
            preciseLng, precisePoint.getLng(), 0.000000000001);
        assertEquals("High precision altitude should be preserved", 
            preciseAlt, precisePoint.getAlt(), 0.000000000001);
    }

    @Test
    public void testExtremeAltitudes() {
        // Test with extreme altitude values
        HistoryPoint historyPoint = new HistoryPoint();
        double highAlt = 8848.86;    // Mount Everest height
        double lowAlt = -11034.0;    // Mariana Trench depth
        
        historyPoint.setAlt(highAlt);
        assertEquals("High altitude should be handled", highAlt, historyPoint.getAlt(), 0.01);
        
        historyPoint.setAlt(lowAlt);
        assertEquals("Low altitude should be handled", lowAlt, historyPoint.getAlt(), 0.01);
    }

    @Test
    public void testMultipleUpdates() {
        HistoryPoint historyPoint = new HistoryPoint();
        
        long[] timestamps = {1640995200000L, 1641081600000L, 1641168000000L};
        double[] lats = {39.9042, 31.2304, 22.3193};
        double[] lngs = {116.4074, 121.4737, 114.1694};
        double[] alts = {43.5, 4.0, 8.0};
        int[] hookGears = {1, 2, 3};
        int[] trolleyGears = {2, 3, 1};
        int[] spinGears = {3, 1, 2};
        
        for (int i = 0; i < timestamps.length; i++) {
            historyPoint.setTimestamp(timestamps[i]);
            historyPoint.setLat(lats[i]);
            historyPoint.setLng(lngs[i]);
            historyPoint.setAlt(alts[i]);
            historyPoint.setHookGear(hookGears[i]);
            historyPoint.setTrolleyGear(trolleyGears[i]);
            historyPoint.setSpinGear(spinGears[i]);
            
            assertEquals("Timestamp should match after update " + i, 
                timestamps[i], historyPoint.getTimestamp());
            assertEquals("Lat should match after update " + i, 
                lats[i], historyPoint.getLat(), 0.000001);
            assertEquals("Lng should match after update " + i, 
                lngs[i], historyPoint.getLng(), 0.000001);
            assertEquals("Alt should match after update " + i, 
                alts[i], historyPoint.getAlt(), 0.000001);
            assertEquals("Hook gear should match after update " + i, 
                hookGears[i], historyPoint.getHookGear());
            assertEquals("Trolley gear should match after update " + i, 
                trolleyGears[i], historyPoint.getTrolleyGear());
            assertEquals("Spin gear should match after update " + i, 
                spinGears[i], historyPoint.getSpinGear());
        }
    }

    @Test
    public void testObjectState() {
        long initialTimestamp = System.currentTimeMillis();
        double initialLat = 39.9042;
        double initialLng = 116.4074;
        double initialAlt = 45.5;
        
        HistoryPoint historyPoint = new HistoryPoint(initialTimestamp, initialLat, initialLng, initialAlt);
        
        // Verify initial state
        assertEquals("Initial timestamp should match", initialTimestamp, historyPoint.getTimestamp());
        assertEquals("Initial lat should match", initialLat, historyPoint.getLat(), 0.000001);
        assertEquals("Initial lng should match", initialLng, historyPoint.getLng(), 0.000001);
        assertEquals("Initial alt should match", initialAlt, historyPoint.getAlt(), 0.000001);
        
        // Change one property
        int newHookGear = 3;
        historyPoint.setHookGear(newHookGear);
        
        // Verify only the changed property is updated
        assertEquals("Hook gear should be updated", newHookGear, historyPoint.getHookGear());
        assertEquals("Timestamp should remain unchanged", initialTimestamp, historyPoint.getTimestamp());
        assertEquals("Lat should remain unchanged", initialLat, historyPoint.getLat(), 0.000001);
        assertEquals("Lng should remain unchanged", initialLng, historyPoint.getLng(), 0.000001);
        assertEquals("Alt should remain unchanged", initialAlt, historyPoint.getAlt(), 0.000001);
    }

    @Test
    public void testHistoryPointEquality() {
        long timestamp = System.currentTimeMillis();
        double lat = 39.9042;
        double lng = 116.4074;
        double alt = 45.5;
        
        HistoryPoint point1 = new HistoryPoint(timestamp, lat, lng, alt);
        HistoryPoint point2 = new HistoryPoint(timestamp, lat, lng, alt);
        HistoryPoint point3 = new HistoryPoint(timestamp + 1000, lat, lng, alt);
        
        // Note: This test assumes HistoryPoint doesn't override equals()
        // Test that objects with same data have same field values
        assertEquals("Same timestamps should match", point1.getTimestamp(), point2.getTimestamp());
        assertEquals("Same lats should match", point1.getLat(), point2.getLat(), 0.000001);
        assertEquals("Same lngs should match", point1.getLng(), point2.getLng(), 0.000001);
        assertEquals("Same alts should match", point1.getAlt(), point2.getAlt(), 0.000001);
        
        // Test that objects with different data have different field values
        assertNotEquals("Different timestamps should not match", 
            point1.getTimestamp(), point3.getTimestamp());
    }

    @Test
    public void testToString() {
        HistoryPoint historyPoint = new HistoryPoint(System.currentTimeMillis(), 39.9042, 116.4074, 45.5);
        
        String toStringResult = historyPoint.toString();
        
        assertNotNull("toString() should not return null", toStringResult);
        assertTrue("toString() should not be empty", toStringResult.length() > 0);
    }

    @Test
    public void testRealWorldTrajectoryScenario() {
        // Test a real-world trajectory scenario for tower crane
        long startTime = System.currentTimeMillis();
        
        // Construction site coordinates (Beijing)
        double siteLat = 39.9088;
        double siteLng = 116.3974;
        double baseAlt = 50.0;
        
        // Create trajectory points for a lifting operation
        HistoryPoint[] trajectory = {
            new HistoryPoint(startTime, siteLat, siteLng, baseAlt),           // Start position
            new HistoryPoint(startTime + 5000, siteLat, siteLng, baseAlt + 10), // Lifting
            new HistoryPoint(startTime + 10000, siteLat + 0.0001, siteLng, baseAlt + 15), // Moving
            new HistoryPoint(startTime + 15000, siteLat + 0.0002, siteLng + 0.0001, baseAlt + 20), // Final position
        };
        
        // Set gear values for each point
        trajectory[0].setHookGear(0); // Idle
        trajectory[0].setTrolleyGear(0);
        trajectory[0].setSpinGear(0);
        
        trajectory[1].setHookGear(2); // Lifting
        trajectory[1].setTrolleyGear(0);
        trajectory[1].setSpinGear(0);
        
        trajectory[2].setHookGear(2); // Moving
        trajectory[2].setTrolleyGear(1);
        trajectory[2].setSpinGear(1);
        
        trajectory[3].setHookGear(1); // Lowering
        trajectory[3].setTrolleyGear(0);
        trajectory[3].setSpinGear(0);
        
        // Verify trajectory
        for (int i = 0; i < trajectory.length; i++) {
            HistoryPoint point = trajectory[i];
            assertNotNull("Trajectory point " + i + " should not be null", point);
            assertTrue("Timestamp should be reasonable", point.getTimestamp() >= startTime);
            assertTrue("Latitude should be reasonable", Math.abs(point.getLat()) <= 90.0);
            assertTrue("Longitude should be reasonable", Math.abs(point.getLng()) <= 180.0);
            assertTrue("Altitude should be reasonable", point.getAlt() >= baseAlt);
        }
    }

    @Test
    public void testHistoryPointConsistency() {
        // Test that history point values remain consistent across operations
        for (int i = 0; i < 100; i++) {
            long timestamp = System.currentTimeMillis() + i * 1000;
            double lat = 39.9042 + (i * 0.0001);
            double lng = 116.4074 + (i * 0.0001);
            double alt = 45.5 + (i * 0.1);
            int hookGear = i % 5;
            int trolleyGear = (i + 1) % 5;
            int spinGear = (i + 2) % 5;
            
            HistoryPoint point = new HistoryPoint(timestamp, lat, lng, alt);
            point.setHookGear(hookGear);
            point.setTrolleyGear(trolleyGear);
            point.setSpinGear(spinGear);
            
            assertEquals("Timestamp should be consistent at iteration " + i, 
                timestamp, point.getTimestamp());
            assertEquals("Lat should be consistent at iteration " + i, 
                lat, point.getLat(), 0.000001);
            assertEquals("Lng should be consistent at iteration " + i, 
                lng, point.getLng(), 0.000001);
            assertEquals("Alt should be consistent at iteration " + i, 
                alt, point.getAlt(), 0.000001);
            assertEquals("Hook gear should be consistent at iteration " + i, 
                hookGear, point.getHookGear());
            assertEquals("Trolley gear should be consistent at iteration " + i, 
                trolleyGear, point.getTrolleyGear());
            assertEquals("Spin gear should be consistent at iteration " + i, 
                spinGear, point.getSpinGear());
        }
    }
}
