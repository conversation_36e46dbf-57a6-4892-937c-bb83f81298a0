package com.fj.towercontrol.data.entity;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * BatteryInfo实体类测试
 * 
 * <AUTHOR>
 */
public class BatteryInfoTest {

    @Test
    public void testConstructor() {
        double testPercent = 85.5;
        int testStatus = 1;
        
        BatteryInfo batteryInfo = new BatteryInfo(testPercent, testStatus);
        
        assertNotNull("BatteryInfo should not be null", batteryInfo);
        assertEquals("Percent should match", testPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Status should match", testStatus, batteryInfo.getStatus());
    }

    @Test
    public void testConstructorWithZeroPercent() {
        double zeroPercent = 0.0;
        int testStatus = 0;
        
        BatteryInfo batteryInfo = new BatteryInfo(zeroPercent, testStatus);
        
        assertEquals("Zero percent should be handled", zeroPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Status should match", testStatus, batteryInfo.getStatus());
    }

    @Test
    public void testConstructorWithMaxPercent() {
        double maxPercent = 100.0;
        int testStatus = 2;
        
        BatteryInfo batteryInfo = new BatteryInfo(maxPercent, testStatus);
        
        assertEquals("Max percent should be handled", maxPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Status should match", testStatus, batteryInfo.getStatus());
    }

    @Test
    public void testPercentGetterSetter() {
        BatteryInfo batteryInfo = new BatteryInfo(50.0, 1);
        
        double newPercent = 75.5;
        batteryInfo.setPercent(newPercent);
        
        assertEquals("Updated percent should match", newPercent, batteryInfo.getPercent(), 0.001);
    }

    @Test
    public void testStatusGetterSetter() {
        BatteryInfo batteryInfo = new BatteryInfo(50.0, 1);
        
        int newStatus = 2;
        batteryInfo.setStatus(newStatus);
        
        assertEquals("Updated status should match", newStatus, batteryInfo.getStatus());
    }

    @Test
    public void testBatteryStatusValues() {
        // Test all valid battery status values: 0静止，1充电，2放电
        int[] statusValues = {0, 1, 2};
        
        for (int status : statusValues) {
            BatteryInfo batteryInfo = new BatteryInfo(50.0, status);
            assertEquals("Status " + status + " should be stored correctly", 
                status, batteryInfo.getStatus());
        }
    }

    @Test
    public void testPercentPrecision() {
        double precisePercent = 85.123456789;
        BatteryInfo batteryInfo = new BatteryInfo(precisePercent, 1);
        
        assertEquals("Precise percent should be preserved", 
            precisePercent, batteryInfo.getPercent(), 0.000000001);
    }

    @Test
    public void testNegativePercent() {
        double negativePercent = -10.0;
        BatteryInfo batteryInfo = new BatteryInfo(negativePercent, 0);
        
        assertEquals("Negative percent should be stored", 
            negativePercent, batteryInfo.getPercent(), 0.001);
    }

    @Test
    public void testOverMaxPercent() {
        double overMaxPercent = 150.0;
        BatteryInfo batteryInfo = new BatteryInfo(overMaxPercent, 1);
        
        assertEquals("Over max percent should be stored", 
            overMaxPercent, batteryInfo.getPercent(), 0.001);
    }

    @Test
    public void testNegativeStatus() {
        int negativeStatus = -1;
        BatteryInfo batteryInfo = new BatteryInfo(50.0, negativeStatus);
        
        assertEquals("Negative status should be stored", negativeStatus, batteryInfo.getStatus());
    }

    @Test
    public void testLargeStatus() {
        int largeStatus = 999;
        BatteryInfo batteryInfo = new BatteryInfo(50.0, largeStatus);
        
        assertEquals("Large status should be stored", largeStatus, batteryInfo.getStatus());
    }

    @Test
    public void testMultipleUpdates() {
        BatteryInfo batteryInfo = new BatteryInfo(0.0, 0);
        
        double[] percentValues = {25.5, 50.0, 75.7, 100.0};
        int[] statusValues = {1, 2, 1, 0};
        
        for (int i = 0; i < percentValues.length; i++) {
            batteryInfo.setPercent(percentValues[i]);
            batteryInfo.setStatus(statusValues[i]);
            
            assertEquals("Percent should match after update " + i, 
                percentValues[i], batteryInfo.getPercent(), 0.001);
            assertEquals("Status should match after update " + i, 
                statusValues[i], batteryInfo.getStatus());
        }
    }

    @Test
    public void testObjectState() {
        double initialPercent = 50.0;
        int initialStatus = 1;
        
        BatteryInfo batteryInfo = new BatteryInfo(initialPercent, initialStatus);
        
        // Verify initial state
        assertEquals("Initial percent should match", initialPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Initial status should match", initialStatus, batteryInfo.getStatus());
        
        // Change one property
        double newPercent = 75.0;
        batteryInfo.setPercent(newPercent);
        
        // Verify only the changed property is updated
        assertEquals("Percent should be updated", newPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Status should remain unchanged", initialStatus, batteryInfo.getStatus());
        
        // Change the other property
        int newStatus = 2;
        batteryInfo.setStatus(newStatus);
        
        // Verify both properties
        assertEquals("Percent should remain as updated", newPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Status should be updated", newStatus, batteryInfo.getStatus());
    }

    @Test
    public void testBatteryInfoEquality() {
        BatteryInfo battery1 = new BatteryInfo(85.5, 1);
        BatteryInfo battery2 = new BatteryInfo(85.5, 1);
        BatteryInfo battery3 = new BatteryInfo(90.0, 1);
        BatteryInfo battery4 = new BatteryInfo(85.5, 2);
        
        // Note: This test assumes BatteryInfo doesn't override equals()
        // Test that objects with same data have same field values
        assertEquals("Same percent should match", battery1.getPercent(), battery2.getPercent(), 0.001);
        assertEquals("Same status should match", battery1.getStatus(), battery2.getStatus());
        
        // Test that objects with different data have different field values
        assertNotEquals("Different percents should not match", 
            battery1.getPercent(), battery3.getPercent(), 0.001);
        assertNotEquals("Different statuses should not match", 
            battery1.getStatus(), battery4.getStatus());
    }

    @Test
    public void testBatteryInfoToString() {
        BatteryInfo batteryInfo = new BatteryInfo(85.5, 1);
        
        String toStringResult = batteryInfo.toString();
        
        assertNotNull("toString() should not return null", toStringResult);
        assertTrue("toString() should not be empty", toStringResult.length() > 0);
    }

    @Test
    public void testBatteryInfoWithExtremeValues() {
        // Test with extreme double values
        double minValue = Double.MIN_VALUE;
        double maxValue = Double.MAX_VALUE;
        
        BatteryInfo batteryMin = new BatteryInfo(minValue, 0);
        assertEquals("Min double value should be handled", minValue, batteryMin.getPercent(), 0.0);
        
        BatteryInfo batteryMax = new BatteryInfo(maxValue, 0);
        assertEquals("Max double value should be handled", maxValue, batteryMax.getPercent(), 0.0);
        
        // Test with extreme integer values
        int minInt = Integer.MIN_VALUE;
        int maxInt = Integer.MAX_VALUE;
        
        BatteryInfo batteryMinInt = new BatteryInfo(50.0, minInt);
        assertEquals("Min integer value should be handled", minInt, batteryMinInt.getStatus());
        
        BatteryInfo batteryMaxInt = new BatteryInfo(50.0, maxInt);
        assertEquals("Max integer value should be handled", maxInt, batteryMaxInt.getStatus());
    }

    @Test
    public void testBatteryInfoWithSpecialDoubleValues() {
        // Test with special double values
        BatteryInfo batteryPosInf = new BatteryInfo(Double.POSITIVE_INFINITY, 0);
        assertEquals("Positive infinity should be preserved", 
            Double.POSITIVE_INFINITY, batteryPosInf.getPercent(), 0.0);
        
        BatteryInfo batteryNegInf = new BatteryInfo(Double.NEGATIVE_INFINITY, 0);
        assertEquals("Negative infinity should be preserved", 
            Double.NEGATIVE_INFINITY, batteryNegInf.getPercent(), 0.0);
        
        BatteryInfo batteryNaN = new BatteryInfo(Double.NaN, 0);
        assertTrue("NaN should be preserved", Double.isNaN(batteryNaN.getPercent()));
    }

    @Test
    public void testBatteryInfoConsistency() {
        // Test that the object behaves consistently across operations
        for (int i = 0; i < 100; i++) {
            double testPercent = i * 1.0;
            int testStatus = i % 3; // 0, 1, 2
            
            BatteryInfo batteryInfo = new BatteryInfo(testPercent, testStatus);
            
            assertEquals("Percent should be consistent at iteration " + i, 
                testPercent, batteryInfo.getPercent(), 0.001);
            assertEquals("Status should be consistent at iteration " + i, 
                testStatus, batteryInfo.getStatus());
        }
    }

    @Test
    public void testBatteryInfoRealWorldScenario() {
        // Test a real-world scenario similar to what's used in the app
        // Based on the code: double percent = data[2] * 1.0 / 10;
        short rawData = 800; // Raw data from modbus
        double calculatedPercent = rawData * 1.0 / 10; // 80.0%
        int status = 1; // Charging
        
        BatteryInfo batteryInfo = new BatteryInfo(calculatedPercent, status);
        
        assertEquals("Real-world percent calculation should work", 
            80.0, batteryInfo.getPercent(), 0.001);
        assertEquals("Real-world status should work", 1, batteryInfo.getStatus());
    }

    @Test
    public void testBatteryInfoStatusMeaning() {
        // Test the documented status meanings: 0静止，1充电，2放电
        BatteryInfo idleBattery = new BatteryInfo(50.0, 0);    // 静止
        BatteryInfo chargingBattery = new BatteryInfo(50.0, 1); // 充电
        BatteryInfo dischargingBattery = new BatteryInfo(50.0, 2); // 放电
        
        assertEquals("Idle status should be 0", 0, idleBattery.getStatus());
        assertEquals("Charging status should be 1", 1, chargingBattery.getStatus());
        assertEquals("Discharging status should be 2", 2, dischargingBattery.getStatus());
        
        // All should have same percent
        assertEquals("All batteries should have same percent", 
            idleBattery.getPercent(), chargingBattery.getPercent(), 0.001);
        assertEquals("All batteries should have same percent", 
            chargingBattery.getPercent(), dischargingBattery.getPercent(), 0.001);
    }

    @Test
    public void testBatteryInfoImmutabilityOfConstructorParams() {
        double originalPercent = 85.5;
        int originalStatus = 1;
        
        BatteryInfo batteryInfo = new BatteryInfo(originalPercent, originalStatus);
        
        // Verify initial values
        assertEquals("Initial percent should match", originalPercent, batteryInfo.getPercent(), 0.001);
        assertEquals("Initial status should match", originalStatus, batteryInfo.getStatus());
        
        // Modify the original variables (primitives, so this doesn't affect the object)
        originalPercent = 90.0;
        originalStatus = 2;
        
        // BatteryInfo should still have original values
        assertEquals("BatteryInfo percent should not change", 85.5, batteryInfo.getPercent(), 0.001);
        assertEquals("BatteryInfo status should not change", 1, batteryInfo.getStatus());
    }
}
