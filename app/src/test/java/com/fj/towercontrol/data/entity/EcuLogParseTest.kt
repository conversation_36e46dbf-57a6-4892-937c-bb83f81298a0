package com.fj.towercontrol.data.entity

import org.junit.Assert.assertNotNull
import org.junit.Test

class EcuLogParseTest {
	@Test
	fun parse_minimal_log() {
		val buf = ByteArray(100) { 0 }
		// Place few plausible values at the beginning (timestamp, code, level). Offsets should be aligned with fromBytes.
		buf[0] = 0x00; buf[1] = 0x00; buf[2] = 0x01; buf[3] = 0x23.toByte() // timestamp high
		buf[4] = 0x45.toByte(); buf[5] = 0x67.toByte(); buf[6] = 0x89.toByte(); buf[7] =
			0x00 // timestamp low
		buf[8] = 0x01 // code
		buf[9] = 0x02 // level

		val log = EcuLog.parse(buf)
		assertNotNull(log)
		// Further asserts can be refined once the exact layout is confirmed
	}
}

